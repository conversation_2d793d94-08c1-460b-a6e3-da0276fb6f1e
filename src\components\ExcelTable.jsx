import React from "react";

const ExcelTable = ({ data }) => {
  if (!data || data.length === 0) return <p>No data loaded.</p>;

  // Define the headers we want to display in order
  const headers = [
    'SKU SIZE',
    'SKU NAME',
    'Selling Price Per Case',
    'Open Market Pack Price',
    'NG Pack Price',
    'Small Supermarket Pack Price'
  ];

  const formatValue = (value) => {
    if (value instanceof Date) {
      return value.toLocaleString();
    }
    if (typeof value === 'number') {
      return value.toLocaleString(); // Add thousand separators
    }
    return value?.toString() || '';
  };

  return (
    <table className="table-auto w-full text-left border mt-6">
      <thead className="bg-gray-100">
        <tr>
          {headers.map((header, i) => (
            <th key={i} className="border px-4 py-2">
              {header}
            </th>
          ))}
        </tr>
      </thead>
      <tbody>
        {data.map((row, rowIndex) => (
          <tr key={rowIndex}>
            {headers.map((header, cellIndex) => (
              <td key={cellIndex} className="border px-4 py-2">
                {formatValue(row[header])}
              </td>
            ))}
          </tr>
        ))}
      </tbody>
    </table>
  );
};

export default ExcelTable;
