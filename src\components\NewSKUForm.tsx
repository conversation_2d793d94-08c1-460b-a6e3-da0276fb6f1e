import React, { useState } from 'react';
import { ProductFormData } from './ProductSelectionForm';

interface NewSKUFormProps {
  selectedCategory: string;
  onBack: () => void;
  onSubmit: (data: ProductFormData) => void;
  fontSize?: string;
}

// Market type options
const MARKET_TYPES = [
  { value: 'OPEN_MARKET', label: 'Open Market' },
  { value: 'NG', label: 'NG' },
  { value: 'SMALL_SUPERMARKET', label: 'Small Supermarket' },
  { value: 'WHOLESALE', label: 'Wholesale' }
];

// SKU Size options
const SKU_SIZES = [
  'SMALL PACK',
  'REGULAR PACK',
  'LARGE PACK',
  'FAMILY PACK',
  'ECONOMY PACK'
];

const NewSKUForm: React.FC<NewSKUFormProps> = ({
  selectedCategory,
  onBack,
  onSubmit,
  fontSize = 'text-sm'
}) => {
  const [formData, setFormData] = useState({
    sku_name: '',
    sku_description: '',
    brand: '',
    sku_size: '',
    market_type: '',
    price: ''
  });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  // Handle input changes
  const handleChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.sku_name.trim()) {
      newErrors.sku_name = 'SKU Name is required';
    }

    if (!formData.sku_description.trim()) {
      newErrors.sku_description = 'SKU Description is required';
    }

    if (!formData.brand.trim()) {
      newErrors.brand = 'Brand is required';
    }

    if (!formData.sku_size) {
      newErrors.sku_size = 'SKU Size is required';
    }

    if (!formData.market_type) {
      newErrors.market_type = 'Market Type is required';
    }

    if (!formData.price || parseFloat(formData.price) <= 0) {
      newErrors.price = 'Valid price is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = () => {
    if (!validateForm()) {
      return;
    }

    const price = parseFloat(formData.price);
    
    const submissionData: ProductFormData = {
      sku_name: formData.sku_name.trim(),
      sku_description: formData.sku_description.trim(),
      sku_category: selectedCategory,
      sku_size: formData.sku_size,
      brand: formData.brand.trim(),
      market_type: formData.market_type,
      location: 'Lagos', // Default location
      kd_case: price * 6, // Assuming 6 units per case
      kd_unit: price,
      wholesale_price: 0,
      open_market_price: 0,
      ng_price: 0,
      small_supermarket_price: 0,
      is_unilever: false
    };

    onSubmit(submissionData);
  };

  return (
    <div className="max-w-md mx-auto bg-white min-h-screen">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-500 to-blue-600 text-white p-4 rounded-b-lg shadow-lg">
        <div className="flex items-center mb-2">
          <button 
            onClick={onBack}
            className="mr-3 text-white hover:bg-white/20 p-1 rounded"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <div>
            <h1 className={`${fontSize === 'text-sm' ? 'text-lg' : fontSize} font-bold`}>Create New SKU</h1>
            <p className="text-blue-100 text-sm">Category: {selectedCategory}</p>
          </div>
        </div>
      </div>

      <div className="p-4 space-y-4">
        {/* SKU Name */}
        <div>
          <label className={`block ${fontSize === 'text-sm' ? 'text-sm' : fontSize} font-medium text-gray-700 mb-2`}>
            SKU Name *
          </label>
          <input
            type="text"
            value={formData.sku_name}
            onChange={(e) => handleChange('sku_name', e.target.value)}
            className={`w-full p-3 border-2 rounded-lg focus:ring-blue-500 focus:border-blue-500 ${
              errors.sku_name ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter SKU name (e.g., Maggi Cube 4g)"
          />
          {errors.sku_name && (
            <p className="text-red-500 text-xs mt-1">{errors.sku_name}</p>
          )}
        </div>

        {/* SKU Description */}
        <div>
          <label className={`block ${fontSize === 'text-sm' ? 'text-sm' : fontSize} font-medium text-gray-700 mb-2`}>
            SKU Description *
          </label>
          <textarea
            value={formData.sku_description}
            onChange={(e) => handleChange('sku_description', e.target.value)}
            className={`w-full p-3 border-2 rounded-lg focus:ring-blue-500 focus:border-blue-500 ${
              errors.sku_description ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter detailed description"
            rows={3}
          />
          {errors.sku_description && (
            <p className="text-red-500 text-xs mt-1">{errors.sku_description}</p>
          )}
        </div>

        {/* Brand */}
        <div>
          <label className={`block ${fontSize === 'text-sm' ? 'text-sm' : fontSize} font-medium text-gray-700 mb-2`}>
            Brand *
          </label>
          <input
            type="text"
            value={formData.brand}
            onChange={(e) => handleChange('brand', e.target.value)}
            className={`w-full p-3 border-2 rounded-lg focus:ring-blue-500 focus:border-blue-500 ${
              errors.brand ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter brand name (e.g., MAGGI)"
          />
          {errors.brand && (
            <p className="text-red-500 text-xs mt-1">{errors.brand}</p>
          )}
        </div>

        {/* SKU Size */}
        <div>
          <label className={`block ${fontSize === 'text-sm' ? 'text-sm' : fontSize} font-medium text-gray-700 mb-2`}>
            SKU Size *
          </label>
          <select
            value={formData.sku_size}
            onChange={(e) => handleChange('sku_size', e.target.value)}
            className={`w-full p-3 border-2 rounded-lg focus:ring-blue-500 focus:border-blue-500 bg-white ${
              errors.sku_size ? 'border-red-500' : 'border-gray-300'
            }`}
          >
            <option value="">Select SKU Size</option>
            {SKU_SIZES.map(size => (
              <option key={size} value={size}>
                {size}
              </option>
            ))}
          </select>
          {errors.sku_size && (
            <p className="text-red-500 text-xs mt-1">{errors.sku_size}</p>
          )}
        </div>

        {/* Market Type */}
        <div>
          <label className={`block ${fontSize === 'text-sm' ? 'text-sm' : fontSize} font-medium text-gray-700 mb-2`}>
            Market Type *
          </label>
          <select
            value={formData.market_type}
            onChange={(e) => handleChange('market_type', e.target.value)}
            className={`w-full p-3 border-2 rounded-lg focus:ring-blue-500 focus:border-blue-500 bg-white ${
              errors.market_type ? 'border-red-500' : 'border-gray-300'
            }`}
          >
            <option value="">Select Market Type</option>
            {MARKET_TYPES.map(market => (
              <option key={market.value} value={market.value}>
                {market.label}
              </option>
            ))}
          </select>
          {errors.market_type && (
            <p className="text-red-500 text-xs mt-1">{errors.market_type}</p>
          )}
        </div>

        {/* Price */}
        <div>
          <label className={`block ${fontSize === 'text-sm' ? 'text-sm' : fontSize} font-medium text-gray-700 mb-2`}>
            Price (₦) *
          </label>
          <input
            type="number"
            value={formData.price}
            onChange={(e) => handleChange('price', e.target.value)}
            className={`w-full p-3 border-2 rounded-lg focus:ring-blue-500 focus:border-blue-500 ${
              errors.price ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter price"
            min="0"
            step="0.01"
          />
          {errors.price && (
            <p className="text-red-500 text-xs mt-1">{errors.price}</p>
          )}
        </div>

        {/* Submit Button */}
        <div className="pt-4">
          <button
            onClick={handleSubmit}
            className="w-full bg-gradient-to-r from-green-500 to-blue-600 text-white py-3 px-6 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]"
          >
            Create SKU
            <svg className="w-5 h-5 ml-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </button>
        </div>

        {/* Info Box */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mt-4">
          <div className="flex items-start">
            <svg className="w-5 h-5 text-blue-500 mt-0.5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div>
              <p className="text-blue-800 text-xs font-medium">Creating New SKU</p>
              <p className="text-blue-700 text-xs mt-1">
                This will create a new product entry in the {selectedCategory} category for the selected market type.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NewSKUForm;
