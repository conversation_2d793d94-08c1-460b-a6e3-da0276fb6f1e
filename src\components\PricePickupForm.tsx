import React, { useState } from 'react';
import { api } from '../services/api';
import { ProductFormData, Product } from './ProductSelectionForm';
import CategorySelection from './CategorySelection';
import SKUPriceForm from './SKUPriceForm';
import NewSKUCategorySelection from './NewSKUCategorySelection';
import NewSKUMarketSelection from './NewSKUMarketSelection';
import NewSKUDetailsForm from './NewSKUDetailsForm';

// Define the form steps
type FormStep = 'category' | 'sku-price' | 'new-sku-category' | 'new-sku-market' | 'new-sku-details';

const PricePickupForm: React.FC = () => {
  // Add step state for multi-level form
  const [currentStep, setCurrentStep] = useState<FormStep>('category');

  const [formData, setFormData] = useState<ProductFormData>({
    sku_name: '',
    sku_description: '',
    sku_category: '',
    sku_size: '', // Changed from sku_size_category to match backend
    brand: '',
    market_type: '',
    kd_case: 0, // Changed from selling_price_case to match backend
    kd_unit: 0, // Changed from selling_price_unit to match backend
    location: '',
    wholesale_price: 0,
    open_market_price: 0,
    ng_price: 0,
    small_supermarket_price: 0,
    is_unilever: false
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);

  // New SKU creation state
  const [newSKUCategory, setNewSKUCategory] = useState<string>('');
  const [newSKUMarket, setNewSKUMarket] = useState<string>('');

  // Handle category selection
  const handleCategorySelect = (category: string) => {
    setFormData(prev => ({
      ...prev,
      sku_category: category
    }));
    setCurrentStep('sku-price');
  };

  // Handle create new SKU
  const handleCreateNewSKU = (category: string) => {
    if (category === 'CREATE_NEW') {
      // Start the new SKU creation flow
      setCurrentStep('new-sku-category');
    } else {
      // Direct category selection for new SKU
      setNewSKUCategory(category);
      setCurrentStep('new-sku-market');
    }
  };

  // Handle new SKU category selection
  const handleNewSKUCategorySelect = (category: string) => {
    setNewSKUCategory(category);
    setCurrentStep('new-sku-market');
  };

  // Handle new SKU market selection
  const handleNewSKUMarketSelect = (market: string) => {
    setNewSKUMarket(market);
    setCurrentStep('new-sku-details');
  };

  // Handle navigation
  const handleBackToCategory = () => {
    setCurrentStep('category');
    setFormData(prev => ({
      ...prev,
      sku_category: '',
      sku_name: '',
      brand: ''
    }));
    setSelectedProduct(null);
    setIsEditMode(false);
    // Reset new SKU state
    setNewSKUCategory('');
    setNewSKUMarket('');
  };

  // Handle back to new SKU category
  const handleBackToNewSKUCategory = () => {
    setCurrentStep('new-sku-category');
    setNewSKUMarket('');
  };

  // Handle back to new SKU market
  const handleBackToNewSKUMarket = () => {
    setCurrentStep('new-sku-market');
  };

  // Handle SKU price form submission
  const handleSKUPriceSubmit = async (data: ProductFormData) => {
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      // Test API connection first
      const connectionTest = await api.testConnection();
      if (!connectionTest.success) {
        setError(`API connection error: ${connectionTest.data?.error || 'Unknown error'}. Please check if the API server is running.`);
        setIsSubmitting(false);
        return;
      }

      // Submit the data
      const submitResponse = await api.submitPrice(data);
      console.log('Submit response:', submitResponse);
      setSuccess(`Price data submitted successfully for ${data.sku_name}!`);

      // Reset to category selection after successful submission
      setTimeout(() => {
        setCurrentStep('category');
        setFormData({
          sku_name: '',
          sku_description: '',
          sku_category: '',
          sku_size: '',
          brand: '',
          market_type: '',
          kd_case: 0,
          kd_unit: 0,
          location: '',
          wholesale_price: 0,
          open_market_price: 0,
          ng_price: 0,
          small_supermarket_price: 0,
          is_unilever: false
        });
        setSuccess(null);
      }, 2000);

    } catch (err: any) {
      console.error('Error submitting form:', err);
      let errorMessage = 'Failed to submit price data. Please try again.';

      if (err.response?.data?.detail) {
        errorMessage = `Validation error: ${JSON.stringify(err.response.data.detail)}`;
      } else if (err.response?.data && typeof err.response.data === 'string') {
        errorMessage = `Error: ${err.response.data}`;
      }

      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle form field changes
  const handleFormChange = (name: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle product selection
  const handleProductSelect = (product: Product | null) => {
    if (!product) {
      setSelectedProduct(null);
      setIsEditMode(false);
      return;
    }

    setSelectedProduct(product);
    setIsEditMode(true);

    // Update the sku_description field if it exists in the selected product
    if (product.sku_description) {
      handleFormChange('sku_description', product.sku_description);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    // Test API connection first
    try {
      const connectionTest = await api.testConnection();
      if (!connectionTest.success) {
        setError(`API connection error: ${connectionTest.data?.error || 'Unknown error'}. Please check if the API server is running.`);
        setIsSubmitting(false);
        return;
      }

      // Log connection status
      console.log('API connection test result:', connectionTest.data);

      // If using mock data, show a warning but continue
      if (connectionTest.data?.useMockData) {
        console.warn('Using mock data for submission. Changes will not be saved to the backend database.');
      }
    } catch (err) {
      console.error('Error testing API connection:', err);
      setError('Could not connect to the API server. Please check if it is running.');
      setIsSubmitting(false);
      return;
    }

    // Validate required fields
    const requiredFields = [
      { name: 'sku_name', label: 'SKU Name' },
      { name: 'sku_description', label: 'SKU Description' },
      { name: 'sku_category', label: 'SKU Category' },
      { name: 'sku_size', label: 'SKU Size' }, // Updated from sku_size_category
      { name: 'brand', label: 'Brand' },
      { name: 'market_type', label: 'Market Type' },
      { name: 'location', label: 'Location' },
      { name: 'kd_case', label: 'KD Case' }, // Updated from selling_price_case
      { name: 'kd_unit', label: 'KD Unit' } // Updated from selling_price_unit
    ];

    const missingFields = requiredFields.filter(field => !formData[field.name]);

    if (missingFields.length > 0) {
      setError(`Please fill in all required fields: ${missingFields.map(f => f.label).join(', ')}`);
      setIsSubmitting(false);
      return;
    }

    try {
      // Convert string prices to numbers
      const numericKdCase = typeof formData.kd_case === 'string'
        ? parseFloat(formData.kd_case)
        : formData.kd_case || 0;

      const numericKdUnit = typeof formData.kd_unit === 'string'
        ? parseFloat(formData.kd_unit)
        : formData.kd_unit || 0;

      // Convert other price fields
      const numericWholesalePrice = typeof formData.wholesale_price === 'string'
        ? parseFloat(formData.wholesale_price)
        : formData.wholesale_price || 0;

      const numericOpenMarketPrice = typeof formData.open_market_price === 'string'
        ? parseFloat(formData.open_market_price)
        : formData.open_market_price || 0;

      const numericNgPrice = typeof formData.ng_price === 'string'
        ? parseFloat(formData.ng_price)
        : formData.ng_price || 0;

      const numericSmallSupermarketPrice = typeof formData.small_supermarket_price === 'string'
        ? parseFloat(formData.small_supermarket_price)
        : formData.small_supermarket_price || 0;

      // Validate numeric values
      if (isNaN(numericKdCase) || numericKdCase < 0) {
        setError('KD Case must be a valid positive number');
        setIsSubmitting(false);
        return;
      }

      if (isNaN(numericKdUnit) || numericKdUnit < 0) {
        setError('KD Unit must be a valid positive number');
        setIsSubmitting(false);
        return;
      }

      // Prepare data for submission
      const submissionData = {
        sku_name: formData.sku_name.trim(),
        sku_category: formData.sku_category.trim(),
        sku_size: formData.sku_size.trim(), // Changed from sku_size_category
        brand: formData.brand.trim(),
        market_type: formData.market_type.trim(),
        location: formData.location.trim(),
        kd_case: numericKdCase, // Changed from selling_price_case
        kd_unit: numericKdUnit, // Changed from selling_price_unit
        wholesale_price: numericWholesalePrice,
        open_market_price: numericOpenMarketPrice,
        ng_price: numericNgPrice,
        small_supermarket_price: numericSmallSupermarketPrice,
        is_unilever: formData.is_unilever,
        source: 'FORM' // Set source to FORM as per backend model
      };

      // Add sku_description if it exists
      if (formData.sku_description) {
        submissionData.sku_description = formData.sku_description.trim();
      }

      // Log the submission data in the format expected by the backend
      console.log('Formatted submission data for backend:', JSON.stringify(submissionData, null, 2));

      // Log the submission data for debugging
      console.log('Submission data:', submissionData);

      // Only update if we have a selected product with an ID and it's not from CSV
      if (isEditMode && selectedProduct && selectedProduct.id &&
          typeof selectedProduct.id === 'string' && !selectedProduct.id.startsWith('csv-')) {
        // Update existing product
        console.log('Updating existing product:', selectedProduct.id);
        const updateResponse = await api.updatePrice(selectedProduct.id, submissionData);
        console.log('Update response:', updateResponse);
        setSuccess('Price data updated successfully!');
      } else {
        // Submit new data to the API
        console.log('Creating new product entry');
        const submitResponse = await api.submitPrice(submissionData);
        console.log('Submit response:', submitResponse);
        setSuccess('Price data submitted successfully!');
      }

      // Reset form and state after successful submission
      setFormData({
        sku_name: '',
        sku_description: '',
        sku_category: '',
        sku_size: '', // Changed from sku_size_category
        brand: '',
        market_type: '',
        kd_case: 0, // Changed from selling_price_case
        kd_unit: 0, // Changed from selling_price_unit
        location: '',
        wholesale_price: 0,
        open_market_price: 0,
        ng_price: 0,
        small_supermarket_price: 0,
        is_unilever: false
      });

      setSelectedProduct(null);
      setIsEditMode(false);
      setCurrentStep('category'); // Go back to category selection

      // Force a refresh of the product list after submission
      setTimeout(() => {
        // This will trigger a re-render with the success message
        setSuccess('Price data submitted successfully!');

        // Refresh the data from the API
        try {
          console.log('Refreshing data from API after successful submission');
          // Directly fetch the prices
          api.getPrices().then(refreshedData => {
            console.log('Data refreshed successfully:', refreshedData.length, 'products');
          }).catch(refreshError => {
            console.error('Error refreshing data:', refreshError);
          });
        } catch (refreshError) {
          console.error('Error refreshing data after submission:', refreshError);
        }
      }, 1000);
    } catch (err: any) {
      console.error('Error submitting form:', err);

      // Get more detailed error information
      let errorMessage = 'Failed to submit price data. Please try again.';

      if (err.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error('Error response data:', err.response.data);
        console.error('Error response status:', err.response.status);

        // If there's validation error details, show them
        if (err.response.data && err.response.data.detail) {
          errorMessage = `Validation error: ${JSON.stringify(err.response.data.detail)}`;
        } else if (err.response.data && typeof err.response.data === 'string') {
          errorMessage = `Error: ${err.response.data}`;
        } else if (err.response.status === 400) {
          errorMessage = 'Bad request: The server could not process your submission. Please check your data.';
        } else if (err.response.status === 401) {
          errorMessage = 'Authentication error: You are not authorized to perform this action.';
        } else if (err.response.status === 403) {
          errorMessage = 'Permission denied: You do not have permission to perform this action.';
        } else if (err.response.status === 404) {
          errorMessage = 'Resource not found: The requested resource does not exist.';
        } else if (err.response.status === 500) {
          errorMessage = 'Server error: The server encountered an error. Please try again later.';
        }
      } else if (err.request) {
        // The request was made but no response was received
        console.error('Error request:', err.request);
        errorMessage = 'No response from server. Please check your connection.';
      } else {
        // Something happened in setting up the request that triggered an Error
        console.error('Error message:', err.message);
        errorMessage = `Error: ${err.message}`;
      }

      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Render different steps based on current step
  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'category':
        return (
          <CategorySelection
            onCategorySelect={handleCategorySelect}
            onCreateNewSKU={handleCreateNewSKU}
            selectedCategory={formData.sku_category}
            fontSize="text-sm"
          />
        );

      case 'sku-price':
        return (
          <div>
            {error && (
              <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded mx-4">
                {error}
              </div>
            )}

            {success && (
              <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded mx-4">
                {success}
              </div>
            )}

            <SKUPriceForm
              selectedCategory={formData.sku_category}
              onBack={handleBackToCategory}
              onSubmit={handleSKUPriceSubmit}
              fontSize="text-sm"
            />
          </div>
        );

      case 'new-sku-category':
        return (
          <div>
            {error && (
              <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded mx-4">
                {error}
              </div>
            )}

            {success && (
              <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded mx-4">
                {success}
              </div>
            )}

            <NewSKUCategorySelection
              onCategorySelect={handleNewSKUCategorySelect}
              onBack={handleBackToCategory}
              fontSize="text-sm"
            />
          </div>
        );

      case 'new-sku-market':
        return (
          <div>
            {error && (
              <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded mx-4">
                {error}
              </div>
            )}

            {success && (
              <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded mx-4">
                {success}
              </div>
            )}

            <NewSKUMarketSelection
              selectedCategory={newSKUCategory}
              onMarketSelect={handleNewSKUMarketSelect}
              onBack={handleBackToNewSKUCategory}
              fontSize="text-sm"
            />
          </div>
        );

      case 'new-sku-details':
        return (
          <div>
            {error && (
              <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded mx-4">
                {error}
              </div>
            )}

            {success && (
              <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded mx-4">
                {success}
              </div>
            )}

            <NewSKUDetailsForm
              selectedCategory={newSKUCategory}
              selectedMarket={newSKUMarket}
              onSubmit={handleSKUPriceSubmit}
              onBack={handleBackToNewSKUMarket}
              fontSize="text-sm"
            />
          </div>
        );

      default:
        return null;
    }
  };

  return renderCurrentStep();
};

export default PricePickupForm;







