import React, { useState } from 'react';
import { api } from '../services/api';
import ProductSelectionForm, { ProductFormData, Product } from './ProductSelectionForm';

const PricePickupForm: React.FC = () => {
  const [formData, setFormData] = useState<ProductFormData>({
    sku_name: '',
    sku_description: '',
    sku_category: '',
    sku_size_category: '',
    brand: '',
    market_type: '',
    selling_price_case: 0,
    selling_price_unit: 0,
    location: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);

  // Handle form field changes
  const handleFormChange = (name: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle product selection
  const handleProductSelect = (product: Product | null) => {
    if (!product) {
      setSelectedProduct(null);
      setIsEditMode(false);
      return;
    }

    setSelectedProduct(product);
    setIsEditMode(true);

    // Update the sku_description field if it exists in the selected product
    if (product.sku_description) {
      handleFormChange('sku_description', product.sku_description);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    // Test API connection first
    try {
      const connectionTest = await api.testConnection();
      if (!connectionTest.success) {
        setError(`API connection error: ${connectionTest.data?.error || 'Unknown error'}. Please check if the API server is running.`);
        setIsSubmitting(false);
        return;
      }

      // Log connection status
      console.log('API connection test result:', connectionTest.data);

      // If using mock data, show a warning but continue
      if (connectionTest.data?.useMockData) {
        console.warn('Using mock data for submission. Changes will not be saved to the backend database.');
      }
    } catch (err) {
      console.error('Error testing API connection:', err);
      setError('Could not connect to the API server. Please check if it is running.');
      setIsSubmitting(false);
      return;
    }

    // Validate required fields
    const requiredFields = [
      { name: 'sku_name', label: 'SKU Name' },
      { name: 'sku_description', label: 'SKU Description' },
      { name: 'sku_category', label: 'SKU Category' },
      { name: 'sku_size_category', label: 'SKU Size Category' },
      { name: 'brand', label: 'Brand' },
      { name: 'market_type', label: 'Market Type' },
      { name: 'location', label: 'Location' },
      { name: 'selling_price_case', label: 'Selling Price (Case)' },
      { name: 'selling_price_unit', label: 'Selling Price (Unit)' }
    ];

    const missingFields = requiredFields.filter(field => !formData[field.name]);

    if (missingFields.length > 0) {
      setError(`Please fill in all required fields: ${missingFields.map(f => f.label).join(', ')}`);
      setIsSubmitting(false);
      return;
    }

    try {
      // Convert string prices to numbers
      const numericPriceCase = typeof formData.selling_price_case === 'string'
        ? parseFloat(formData.selling_price_case)
        : formData.selling_price_case || 0;

      const numericPriceUnit = typeof formData.selling_price_unit === 'string'
        ? parseFloat(formData.selling_price_unit)
        : formData.selling_price_unit || 0;

      // Validate numeric values
      if (isNaN(numericPriceCase) || numericPriceCase < 0) {
        setError('Selling Price (Case) must be a valid positive number');
        setIsSubmitting(false);
        return;
      }

      if (isNaN(numericPriceUnit) || numericPriceUnit < 0) {
        setError('Selling Price (Unit) must be a valid positive number');
        setIsSubmitting(false);
        return;
      }

      // Prepare data for submission
      const submissionData = {
        sku_name: formData.sku_name.trim(),
        sku_category: formData.sku_category.trim(),
        sku_size_category: formData.sku_size_category.trim(),
        brand: formData.brand.trim(),
        market_type: formData.market_type.trim(),
        location: formData.location.trim(),
        selling_price_case: numericPriceCase,
        selling_price_unit: numericPriceUnit,
        sku_description: formData.sku_description ? formData.sku_description.trim() : ''
      };

      // Log the submission data in the format expected by the backend
      console.log('Formatted submission data for backend:', JSON.stringify(submissionData, null, 2));

      // Log the submission data for debugging
      console.log('Submission data:', submissionData);

      // Only update if we have a selected product with an ID and it's not from CSV
      if (isEditMode && selectedProduct && selectedProduct.id &&
          typeof selectedProduct.id === 'string' && !selectedProduct.id.startsWith('csv-')) {
        // Update existing product
        console.log('Updating existing product:', selectedProduct.id);
        const updateResponse = await api.updatePrice(selectedProduct.id, submissionData);
        console.log('Update response:', updateResponse);
        setSuccess('Price data updated successfully!');
      } else {
        // Submit new data to the API
        console.log('Creating new product entry');
        const submitResponse = await api.submitPrice(submissionData);
        console.log('Submit response:', submitResponse);
        setSuccess('Price data submitted successfully!');
      }

      // Reset form and state after successful submission
      setFormData({
        sku_name: '',
        sku_description: '',
        sku_category: '',
        sku_size_category: '',
        brand: '',
        market_type: '',
        selling_price_case: 0,
        selling_price_unit: 0,
        location: ''
      });

      setSelectedProduct(null);
      setIsEditMode(false);

      // Force a refresh of the product list after submission
      setTimeout(() => {
        // This will trigger a re-render with the success message
        setSuccess('Price data submitted successfully!');

        // Refresh the data from the API
        try {
          console.log('Refreshing data from API after successful submission');
          // Directly fetch the prices
          api.getPrices().then(refreshedData => {
            console.log('Data refreshed successfully:', refreshedData.length, 'products');
          }).catch(refreshError => {
            console.error('Error refreshing data:', refreshError);
          });
        } catch (refreshError) {
          console.error('Error refreshing data after submission:', refreshError);
        }
      }, 1000);
    } catch (err: any) {
      console.error('Error submitting form:', err);

      // Get more detailed error information
      let errorMessage = 'Failed to submit price data. Please try again.';

      if (err.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error('Error response data:', err.response.data);
        console.error('Error response status:', err.response.status);

        // If there's validation error details, show them
        if (err.response.data && err.response.data.detail) {
          errorMessage = `Validation error: ${JSON.stringify(err.response.data.detail)}`;
        } else if (err.response.data && typeof err.response.data === 'string') {
          errorMessage = `Error: ${err.response.data}`;
        } else if (err.response.status === 400) {
          errorMessage = 'Bad request: The server could not process your submission. Please check your data.';
        } else if (err.response.status === 401) {
          errorMessage = 'Authentication error: You are not authorized to perform this action.';
        } else if (err.response.status === 403) {
          errorMessage = 'Permission denied: You do not have permission to perform this action.';
        } else if (err.response.status === 404) {
          errorMessage = 'Resource not found: The requested resource does not exist.';
        } else if (err.response.status === 500) {
          errorMessage = 'Server error: The server encountered an error. Please try again later.';
        }
      } else if (err.request) {
        // The request was made but no response was received
        console.error('Error request:', err.request);
        errorMessage = 'No response from server. Please check your connection.';
      } else {
        // Something happened in setting up the request that triggered an Error
        console.error('Error message:', err.message);
        errorMessage = `Error: ${err.message}`;
      }

      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-xl font-semibold mb-6">Submit Price Information</h2>

      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      {success && (
        <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
          {success}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Use the shared ProductSelectionForm component */}
        <ProductSelectionForm
          formData={formData}
          onChange={handleFormChange}
          onProductSelect={handleProductSelect}
          isEditMode={isEditMode}
          fontSize="text-sm"
        />

        {/* SKU Description field - specific to this form */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            SKU Description *
          </label>
          <textarea
            name="sku_description"
            value={formData.sku_description || ''}
            onChange={(e) => handleFormChange('sku_description', e.target.value)}
            required
            className="w-full p-2 border rounded-md focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter SKU Description"
            rows={3}
          />
        </div>

        <button
          type="submit"
          disabled={isSubmitting}
          className={`w-full py-2 px-4 border border-transparent rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
            isSubmitting ? 'opacity-50 cursor-not-allowed' : ''
          }`}
        >
          {isSubmitting
            ? 'Submitting...'
            : (isEditMode && selectedProduct && selectedProduct.id &&
               typeof selectedProduct.id === 'string' && !selectedProduct.id.startsWith('csv-'))
              ? 'Update Price'
              : 'Submit Price'}
        </button>
      </form>
    </div>
  );
};

export default PricePickupForm;







