import React, { useState, useEffect, useCallback } from 'react';
import { api } from '../services/api';
import {
  SKU_CATEGORIES,
  SKU_SIZE_CATEGORIES,
  isUnileverBrand,
  getBrandFromSku,
  getSkuCategoryFromBrand,
  getSizeCategoryFromSku,
  isDeodorant,
  isSkinCare
} from '../utils/brandUtils';
import { debounce, containsText } from '../utils/helpers';

// Define interfaces for form data and products
export interface ProductFormData {
  sku_name: string;
  sku_category: string;
  sku_size_category: string;
  brand: string;
  market_type: string;
  location: string;
  selling_price_case: string | number;
  selling_price_unit: string | number;
  sku_description?: string;
}

export interface Product {
  id?: string;
  sku_name: string;
  sku_category?: string;
  sku_size_category?: string;
  brand?: string;
  market_type?: string;
  location?: string;
  selling_price_case?: number;
  selling_price_unit?: number;
  created_at?: string;
  sku_description?: string;
}

interface ProductSelectionFormProps {
  formData: ProductFormData;
  onChange: (name: string, value: any) => void;
  onProductSelect: (product: Product | null) => void;
  isEditMode: boolean;
  className?: string;
  fontSize?: string;
}

const ProductSelectionForm: React.FC<ProductSelectionFormProps> = ({
  formData,
  onChange,
  onProductSelect,
  isEditMode,
  className = '',
  fontSize = 'text-[10px]'
}) => {
  // State for products
  const [allProducts, setAllProducts] = useState<Product[]>([]);
  const [competitorProducts, setCompetitorProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  // Add a separate state for display purposes
  const [displayProduct, setDisplayProduct] = useState<Product | null>(null);
  const [locations, setLocations] = useState<string[]>([]);
  // Add state for search
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [isSearching, setIsSearching] = useState<boolean>(false);

  // Sample data for fallback
  const SAMPLE_PRODUCTS: Product[] = [
    // DEODORANT category
    {
      id: 'sample-1',
      sku_name: 'Nivea Roll on 50ml',
      sku_category: 'DEODORANT',
      sku_size_category: 'REGULAR PACK',
      brand: 'NIVEA',
      market_type: 'RETAIL',
      location: 'Lagos',
      selling_price_case: 2400,
      selling_price_unit: 400,
      created_at: new Date().toISOString()
    },
    {
      id: 'sample-2',
      sku_name: 'Enchanter Roll on 50ml',
      sku_category: 'DEODORANT',
      sku_size_category: 'REGULAR PACK',
      brand: 'ENCHANTER',
      market_type: 'RETAIL',
      location: 'Lagos',
      selling_price_case: 1800,
      selling_price_unit: 300,
      created_at: new Date().toISOString()
    },
    {
      id: 'sample-3',
      sku_name: 'Red Diamond Roll on 50ml',
      sku_category: 'DEODORANT',
      sku_size_category: 'REGULAR PACK',
      brand: 'RED DIAMOND',
      market_type: 'RETAIL',
      location: 'Lagos',
      selling_price_case: 1500,
      selling_price_unit: 250,
      created_at: new Date().toISOString()
    },
    {
      id: 'sample-4',
      sku_name: 'Nivea Roll on 25ml',
      sku_category: 'DEODORANT',
      sku_size_category: 'SMALL PACK',
      brand: 'NIVEA',
      market_type: 'RETAIL',
      location: 'Lagos',
      selling_price_case: 1200,
      selling_price_unit: 200,
      created_at: new Date().toISOString()
    },

    // SKIN CARE category
    {
      id: 'sample-5',
      sku_name: 'Cussons Baby Jelly 50g',
      sku_category: 'SKIN CARE',
      sku_size_category: 'SMALL PACK',
      brand: 'CUSSONS',
      market_type: 'RETAIL',
      location: 'Lagos',
      selling_price_case: 1200,
      selling_price_unit: 200,
      created_at: new Date().toISOString()
    },
    {
      id: 'sample-6',
      sku_name: 'NBC Jelly 250g',
      sku_category: 'SKIN CARE',
      sku_size_category: 'REGULAR PACK',
      brand: 'NBC',
      market_type: 'RETAIL',
      location: 'Lagos',
      selling_price_case: 3000,
      selling_price_unit: 500,
      created_at: new Date().toISOString()
    },
    {
      id: 'sample-7',
      sku_name: 'Fressia Jelly 100g',
      sku_category: 'SKIN CARE',
      sku_size_category: 'REGULAR PACK',
      brand: 'FRESSIA',
      market_type: 'RETAIL',
      location: 'Lagos',
      selling_price_case: 1800,
      selling_price_unit: 300,
      created_at: new Date().toISOString()
    },

    // ORAL CARE category
    {
      id: 'sample-8',
      sku_name: 'Oral B Toothpaste 100g',
      sku_category: 'ORAL CARE',
      sku_size_category: 'REGULAR PACK',
      brand: 'ORAL B',
      market_type: 'RETAIL',
      location: 'Lagos',
      selling_price_case: 3600,
      selling_price_unit: 600,
      created_at: new Date().toISOString()
    },
    {
      id: 'sample-9',
      sku_name: 'Colgate Toothpaste 50g',
      sku_category: 'ORAL CARE',
      sku_size_category: 'SMALL PACK',
      brand: 'COLGATE',
      market_type: 'RETAIL',
      location: 'Lagos',
      selling_price_case: 1800,
      selling_price_unit: 300,
      created_at: new Date().toISOString()
    },

    // NUTRITION category
    {
      id: 'sample-10',
      sku_name: 'Maggi Cube 4g',
      sku_category: 'NUTRITION',
      sku_size_category: 'SMALL PACK',
      brand: 'MAGGI',
      market_type: 'RETAIL',
      location: 'Lagos',
      selling_price_case: 600,
      selling_price_unit: 10,
      created_at: new Date().toISOString()
    },
    {
      id: 'sample-11',
      sku_name: 'Onga Cube 4g',
      sku_category: 'NUTRITION',
      sku_size_category: 'SMALL PACK',
      brand: 'ONGA',
      market_type: 'RETAIL',
      location: 'Lagos',
      selling_price_case: 540,
      selling_price_unit: 9,
      created_at: new Date().toISOString()
    }
  ];

  // Function to fetch products and category choices
  const fetchDataInternal = async (): Promise<void> => {
    try {
      console.log('Fetching data from API and CSV sources...');
      // Fetch category choices
      const categoryData = await api.getCategoryChoices();

      // If the API returns locations, use them
      if (categoryData && categoryData.locations) {
        setLocations(categoryData.locations);
      } else {
        // Fallback to predefined locations
        setLocations(['Lagos', 'Abuja', 'Port Harcourt', 'Kano', 'Ibadan']);
      }

      // Load data from both CSV and API
      let allLoadedProducts: Product[] = [];

      // First try to load from CSV
      try {
        // Import the excelService dynamically to avoid circular dependencies
        const { excelService } = await import('../services/excelService');
        console.log('Loading data from CSV...');
        const csvProducts = await excelService.readDatabaseFiles();
        console.log('CSV data loaded:', csvProducts.length, 'products');

        // Convert CSV products to the Product format
        const formattedCsvProducts = csvProducts.map(csvProduct => ({
          id: `csv-${csvProduct.sku_name}`,
          sku_name: csvProduct.sku_name,
          sku_category: csvProduct.category,
          sku_size_category: csvProduct.sku_size,
          brand: getBrandFromSku(csvProduct.sku_name),
          market_type: 'RETAIL', // Default market type
          location: 'Lagos', // Default location
          selling_price_case: csvProduct.selling_price_case || 0,
          selling_price_unit: csvProduct.kd_unit_price || 0,
          created_at: csvProduct.uploadDate?.toISOString() || new Date().toISOString()
        }));

        allLoadedProducts = [...formattedCsvProducts];
      } catch (csvError) {
        console.error('Error loading CSV data:', csvError);
      }

      // Then try to load from API
      try {
        console.log('Fetching products from API...');
        const apiProducts = await api.getPrices();
        console.log('API products loaded:', apiProducts.length);

        // Combine with CSV products
        allLoadedProducts = [...allLoadedProducts, ...apiProducts];
      } catch (apiError) {
        console.error('Error fetching API products:', apiError);
      }

      // If we have products from either source, use them
      if (allLoadedProducts.length > 0) {
        console.log('Total products loaded:', allLoadedProducts.length);
        setAllProducts(allLoadedProducts);

        // Filter out Unilever products to get only competitor products
        const nonUnileverProducts = allLoadedProducts.filter(
          (product: Product) => !isUnileverBrand(product.sku_name)
        );
        console.log('Non-Unilever products:', nonUnileverProducts.length);
        setCompetitorProducts(nonUnileverProducts);
      } else {
        // If no products from either source, use sample data
        console.log('No products loaded, using sample data');
        setAllProducts(SAMPLE_PRODUCTS);
        setCompetitorProducts(SAMPLE_PRODUCTS);
      }

      console.log('Data fetching completed successfully');
      return Promise.resolve();
    } catch (error) {
      console.error('Error fetching data:', error);
      // Fallback to predefined locations and sample products
      setLocations(['Lagos', 'Abuja', 'Port Harcourt', 'Kano', 'Ibadan']);
      console.log('Using sample products due to API error');
      setAllProducts(SAMPLE_PRODUCTS);
      setCompetitorProducts(SAMPLE_PRODUCTS);
      return Promise.resolve();
    }
  };

  // Use fetchDataInternal directly

  // Fetch products and category choices on component mount
  useEffect(() => {
    fetchDataInternal();
  }, []);

  // Function to filter products based on category and search term
  const filterProducts = useCallback(() => {
    try {
      if (!formData.sku_category) {
        setFilteredProducts([]);
        setIsSearching(false);
        return;
      }

      console.log('Filtering products - Category:', formData.sku_category, 'Search term:', searchTerm);
      console.log('Competitor products available:', competitorProducts.length);

      // Safety check for competitorProducts
      if (!competitorProducts || !Array.isArray(competitorProducts)) {
        console.error('competitorProducts is not an array:', competitorProducts);
        setFilteredProducts([]);
        setIsSearching(false);
        return;
      }

      // First filter by category
      const categoryFiltered = competitorProducts.filter(product => {
        try {
          // Safety check for product
          if (!product || typeof product !== 'object') {
            return false;
          }

          // Check if it's a deodorant and the filter is for deodorants
          if (formData.sku_category.toUpperCase() === 'DEODORANT' && isDeodorant(product.sku_name)) {
            return true;
          }

          // Check if it's a skin care product and the filter is for skin care
          if (formData.sku_category.toUpperCase() === 'SKIN CARE' && isSkinCare(product.sku_name)) {
            return true;
          }

          // If product has a category, use it
          if (product.sku_category) {
            return product.sku_category.toUpperCase() === formData.sku_category.toUpperCase();
          }

          // Otherwise try to determine category from brand or SKU name
          const brand = product.brand || getBrandFromSku(product.sku_name);
          if (brand) {
            const category = getSkuCategoryFromBrand(brand);
            return category && category.toUpperCase() === formData.sku_category.toUpperCase();
          }

          // For CSV products, check the category field
          if (product.id && typeof product.id === 'string' && product.id.startsWith('csv-') && product.sku_category) {
            return product.sku_category.toUpperCase() === formData.sku_category.toUpperCase();
          }

          return false;
        } catch (filterError) {
          console.error('Error filtering product:', filterError, product);
          return false;
        }
      });

      // Then filter by search term if one exists
      let searchFiltered = categoryFiltered;
      if (searchTerm && searchTerm.trim()) {
        searchFiltered = categoryFiltered.filter(product => {
          try {
            // Search in SKU name
            if (product.sku_name && containsText(product.sku_name, searchTerm)) {
              return true;
            }

            // Search in brand
            if (product.brand && containsText(product.brand, searchTerm)) {
              return true;
            }

            // Search in description
            if (product.sku_description && containsText(product.sku_description, searchTerm)) {
              return true;
            }

            return false;
          } catch (searchError) {
            console.error('Error searching product:', searchError, product);
            return false;
          }
        });
      }

      console.log('Filtered products:', searchFiltered.length);

      // Sort filtered products by name for easier selection
      const sortedFiltered = [...searchFiltered].sort((a, b) => {
        try {
          return a.sku_name.localeCompare(b.sku_name);
        } catch (sortError) {
          console.error('Error sorting products:', sortError, a, b);
          return 0;
        }
      });

      setFilteredProducts(sortedFiltered);
      setIsSearching(false);
    } catch (error) {
      console.error('Error in filterProducts:', error);
      // Prevent the app from crashing by handling the error
      setFilteredProducts([]);
      setIsSearching(false);
    }
  }, [formData.sku_category, competitorProducts, searchTerm]);

  // Create a debounced version of the filter function
  const debouncedFilterProducts = useCallback(
    debounce(() => {
      filterProducts();
    }, 300),
    [filterProducts]
  );

  // Handle search term changes
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    setIsSearching(true);
    debouncedFilterProducts();
  };

  // Filter products when category or search term changes
  useEffect(() => {
    try {
      console.log('Running filter effect with category:', formData.sku_category);
      filterProducts();
    } catch (error) {
      console.error('Error in filter effect:', error);
    }
  }, [filterProducts]);

  // Track if category change is from direct user input or from product selection
  const [isChangingFromProductSelection, setIsChangingFromProductSelection] = useState(false);

  // Handle form field changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement> | { name: string, value: string, fromProductSelection?: boolean }) => {
    try {
      // Handle both event objects and direct parameter objects
      const name = 'target' in e ? e.target.name : e.name;
      const value = 'target' in e ? e.target.value : e.value;
      const fromProductSelection = !('target' in e) && e.fromProductSelection;

      console.log(`Field changed: ${name} = ${value}${fromProductSelection ? ' (from product selection)' : ''}`);

      // If changing category and NOT from product selection, reset product selection
      if (name === 'sku_category' && !fromProductSelection && !isChangingFromProductSelection) {
        console.log('Category changed by user, resetting product selection');
        setSelectedProduct(null);
        setDisplayProduct(null);
        onProductSelect(null);

        // Reset search term when category changes
        setSearchTerm('');
        setIsSearching(false);

        // Show loading message while filtering
        if (value) {
          setFilteredProducts([{ id: 'loading', sku_name: 'Loading products...', brand: '' } as Product]);
        } else {
          setFilteredProducts([]);
        }
      }

      // Call parent onChange handler
      onChange(name, value);

      // If category changed by user (not from product selection), trigger filtering after a short delay
      if (name === 'sku_category' && value && !fromProductSelection && !isChangingFromProductSelection) {
        setTimeout(() => {
          try {
            filterProducts();
          } catch (filterError) {
            console.error('Error filtering after category change:', filterError);
          }
        }, 100);
      }
    } catch (error) {
      console.error('Error in handleChange:', error);
    }
  };

  // Handle product selection
  const handleProductSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    try {
      const productId = e.target.value;

      if (!productId) {
        setSelectedProduct(null);
        setDisplayProduct(null);
        onProductSelect(null);
        return;
      }

      console.log('Product ID selected:', productId);
      console.log('Available competitor products:', competitorProducts.length);

      // Find the product in all competitor products
      // Convert IDs to strings for comparison to handle numeric IDs
      const product = competitorProducts.find(p =>
        (p.id !== undefined && p.id !== null && p.id.toString() === productId) ||
        p.sku_name === productId
      );

      // Log all product IDs for debugging
      console.log('Available product IDs:', competitorProducts.map(p => p.id));

      if (product) {
        console.log('Product selected:', product);

        // Always set the display product for the dropdown
        setDisplayProduct(product);

        // Determine if this is a CSV product (create) or API product (update)
        const isFromCsv = product.id && typeof product.id === 'string' && product.id.startsWith('csv-');
        console.log('Product source:', isFromCsv ? 'CSV (will create new)' : 'API (will update)');

        // If it's from CSV, we'll create a new entry, so don't set it as selected for update
        if (isFromCsv) {
          // For CSV products, we'll use the data but not set it as the selected product for update
          setSelectedProduct(null);
        } else {
          // For API products, we'll set it as the selected product for update
          setSelectedProduct(product);
        }

        // We're not using the product's category anymore, but keeping this code for reference
        // const skuCategory = product.sku_category ||
        //                   (isDeodorant(product.sku_name) ? 'DEODORANT' :
        //                   (isSkinCare(product.sku_name) ? 'SKIN CARE' :
        //                   getSkuCategoryFromBrand(product.brand || getBrandFromSku(product.sku_name) || '')));

        // Determine SKU size category if not present
        const skuSizeCategory = product.sku_size_category || getSizeCategoryFromSku(product.sku_name) || '';

        // Determine brand if not present
        const brand = product.brand || getBrandFromSku(product.sku_name) || '';

        // Set flag to indicate we're changing from product selection
        setIsChangingFromProductSelection(true);

        // Update form data with selected product details - use safe values with fallbacks
        handleChange({ name: 'sku_name', value: product.sku_name || '', fromProductSelection: true });

        // Do NOT update the category - preserve the current category
        // handleChange({ name: 'sku_category', value: skuCategory || formData.sku_category || '', fromProductSelection: true });

        handleChange({ name: 'sku_size_category', value: skuSizeCategory || '', fromProductSelection: true });
        handleChange({ name: 'brand', value: brand || '', fromProductSelection: true });
        handleChange({ name: 'market_type', value: product.market_type || 'RETAIL', fromProductSelection: true });
        handleChange({ name: 'location', value: product.location || 'Lagos', fromProductSelection: true });

        // Handle price fields - ensure they're strings
        handleChange({
          name: 'selling_price_case',
          value: (product.selling_price_case !== undefined && product.selling_price_case !== null)
            ? product.selling_price_case.toString()
            : '0',
          fromProductSelection: true
        });

        handleChange({
          name: 'selling_price_unit',
          value: (product.selling_price_unit !== undefined && product.selling_price_unit !== null)
            ? product.selling_price_unit.toString()
            : '0',
          fromProductSelection: true
        });

        // Reset the flag after all changes are made
        setTimeout(() => {
          setIsChangingFromProductSelection(false);
        }, 100);

        // If the product has a description, update it
        if (product.sku_description) {
          handleChange({
            name: 'sku_description',
            value: product.sku_description,
            fromProductSelection: true
          });
        }

        // Notify parent component about the selected product
        // If it's from CSV, pass null as the selected product (create new)
        // If it's from API, pass the product (update existing)
        onProductSelect(isFromCsv ? null : product);

        console.log('Form data updated with selected product details');
        console.log('Edit mode:', isFromCsv ? 'No (creating new)' : 'Yes (updating existing)');
      } else {
        console.warn('Product not found with ID:', productId);
        // Reset selection if product not found
        setSelectedProduct(null);
        setDisplayProduct(null);
        onProductSelect(null);
      }
    } catch (error) {
      console.error('Error in handleProductSelect:', error);
      // Prevent the app from crashing by handling the error
      setSelectedProduct(null);
      setDisplayProduct(null);
      onProductSelect(null);
    }
  };

  return (
    <div className={className}>
      {/* SKU Category - First to filter products */}
      <div>
        <label className={`block ${fontSize} font-medium text-gray-700 mb-1`}>
          SKU Category*
        </label>
        <select
          name="sku_category"
          value={formData.sku_category}
          onChange={handleChange}
          required
          className={`w-full p-2 ${fontSize} border rounded-md focus:ring-blue-500 focus:border-blue-500`}
        >
          <option value="">Select Category</option>
          {SKU_CATEGORIES.map(category => (
            <option key={category} value={category}>
              {category}
            </option>
          ))}
        </select>
      </div>

      {/* Product Selection - Only show when category is selected */}
      {formData.sku_category && (
        <div className="mt-4">
          <div className="flex justify-between items-center mb-1">
            <label className={`block ${fontSize} font-medium text-gray-700`}>
              Select Existing Product
            </label>
            <button
              type="button"
              onClick={() => {
                // Show loading message
                setFilteredProducts([{ id: 'loading', sku_name: 'Loading products...', brand: '' } as Product]);

                // Fetch fresh data
                fetchDataInternal().then(() => {
                  // After data is fetched, filter products
                  setTimeout(() => {
                    filterProducts();
                  }, 500);
                });
              }}
              className={`${fontSize} text-blue-600 hover:text-blue-800 flex items-center`}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh
            </button>
          </div>

          {/* Search input for products */}
          <div className="mb-2">
            <div className="relative">
              <input
                type="text"
                placeholder="Search for products by name or brand..."
                value={searchTerm}
                onChange={handleSearchChange}
                className={`w-full p-2 ${fontSize} border rounded-md focus:ring-blue-500 focus:border-blue-500 pl-8`}
              />
              <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              {isSearching && (
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <svg className="animate-spin h-4 w-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </div>
              )}
            </div>
            {searchTerm && (
              <p className={`${fontSize === 'text-[10px]' ? 'text-[9px]' : 'text-[11px]'} text-gray-500 mt-1`}>
                Searching for "{searchTerm}"
              </p>
            )}
          </div>

          <div className="relative">
            <select
              onChange={handleProductSelect}
              value={displayProduct?.id ? displayProduct.id.toString() : (displayProduct?.sku_name || '')}
              className={`w-full p-2 ${fontSize} border rounded-md focus:ring-blue-500 focus:border-blue-500`}
            >
              <option value="">-- Select a product or enter new details --</option>
              {(() => {
                try {
                  if (filteredProducts.length > 0) {
                    // Check if we're showing the loading message
                    if (filteredProducts[0].id === 'loading') {
                      return <option value="" disabled>Loading products...</option>;
                    }

                    // Prepare arrays for CSV and API products
                    const csvProducts = [];
                    const apiProducts = [];

                    // Safely categorize products
                    for (const product of filteredProducts) {
                      try {
                        // Check if product.id is a string and starts with 'csv-'
                        if (product && product.id && typeof product.id === 'string' && product.id.startsWith('csv-')) {
                          csvProducts.push(product);
                        } else if (product) {
                          apiProducts.push(product);
                        }
                      } catch (productError) {
                        console.error('Error processing product:', productError, product);
                      }
                    }

                    return (
                      <>
                        {/* Group products by source */}
                        {csvProducts.length > 0 && (
                          <optgroup label="CSV Products (Will Create New)">
                            {csvProducts.map((product, index) => (
                              <option
                                key={product.id ? product.id.toString() : `csv-${index}`}
                                value={product.id ? product.id.toString() : product.sku_name}
                              >
                                {product.sku_name} - {product.brand || getBrandFromSku(product.sku_name)} (Create New)
                              </option>
                            ))}
                          </optgroup>
                        )}
                        {apiProducts.length > 0 && (
                          <optgroup label="Submitted Products (Will Update)">
                            {apiProducts.map((product, index) => (
                              <option
                                key={product.id ? product.id.toString() : `api-${index}`}
                                value={product.id ? product.id.toString() : product.sku_name}
                              >
                                {product.sku_name} - {product.brand || getBrandFromSku(product.sku_name)} (Update)
                              </option>
                            ))}
                          </optgroup>
                        )}
                      </>
                    );
                  } else {
                    return <option value="" disabled>No products found in this category</option>;
                  }
                } catch (renderError) {
                  console.error('Error rendering product options:', renderError);
                  return <option value="" disabled>Error loading products</option>;
                }
              })()}
            </select>
          </div>
          {displayProduct ? (
            <div className={`mt-1 ${displayProduct.id && typeof displayProduct.id === 'string' && displayProduct.id.startsWith('csv-') ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'} p-1 rounded-md`}>
              <p className={`${fontSize === 'text-[10px]' ? 'text-[9px]' : 'text-[11px]'} font-medium`}>
                {displayProduct.id && typeof displayProduct.id === 'string' && displayProduct.id.startsWith('csv-')
                  ? 'Creating new entry based on CSV data'
                  : 'Editing existing product from database'}
              </p>
              <p className={`${fontSize === 'text-[10px]' ? 'text-[9px]' : 'text-[11px]'}`}>
                {displayProduct.id && typeof displayProduct.id === 'string' && displayProduct.id.startsWith('csv-')
                  ? 'This will create a new entry in the database'
                  : 'This will update the existing entry in the database'}
              </p>
            </div>
          ) : (
            <p className={`${fontSize === 'text-[10px]' ? 'text-[9px]' : 'text-[11px]'} text-gray-500 mt-1`}>
              {isEditMode ? 'Editing existing product' : 'Enter new product details below or select from dropdown'}
            </p>
          )}
          {filteredProducts.length > 0 && filteredProducts[0].id !== 'loading' && (
            <div className="mt-1">
              <p className={`${fontSize === 'text-[10px]' ? 'text-[9px]' : 'text-[11px]'} text-blue-500`}>
                {filteredProducts.length} products available in this category
              </p>
              <div className="flex justify-between">
                <p className={`${fontSize === 'text-[10px]' ? 'text-[9px]' : 'text-[11px]'} text-gray-500`}>
                  {filteredProducts.filter(p => !(p.id && typeof p.id === 'string' && p.id.startsWith('csv-'))).length} existing products (can update)
                </p>
                <p className={`${fontSize === 'text-[10px]' ? 'text-[9px]' : 'text-[11px]'} text-gray-500`}>
                  {filteredProducts.filter(p => p.id && typeof p.id === 'string' && p.id.startsWith('csv-')).length} new products (will create)
                </p>
              </div>
            </div>
          )}
        </div>
      )}

      {/* SKU Name */}
      <div className="mt-4">
        <label className={`block ${fontSize} font-medium text-gray-700 mb-1`}>
          SKU Name*
        </label>
        <input
          type="text"
          name="sku_name"
          value={formData.sku_name}
          onChange={handleChange}
          required
          className={`w-full p-2 ${fontSize} border rounded-md focus:ring-blue-500 focus:border-blue-500`}
        />
      </div>

      {/* Brand */}
      <div className="mt-4">
        <label className={`block ${fontSize} font-medium text-gray-700 mb-1`}>
          Brand*
        </label>
        <input
          type="text"
          name="brand"
          value={formData.brand}
          onChange={handleChange}
          required
          className={`w-full p-2 ${fontSize} border rounded-md focus:ring-blue-500 focus:border-blue-500`}
        />
      </div>

      {/* SKU Size Category */}
      <div className="mt-4">
        <label className={`block ${fontSize} font-medium text-gray-700 mb-1`}>
          SKU Size Category*
        </label>
        <select
          name="sku_size_category"
          value={formData.sku_size_category}
          onChange={handleChange}
          required
          className={`w-full p-2 ${fontSize} border rounded-md focus:ring-blue-500 focus:border-blue-500`}
        >
          <option value="">Select Size</option>
          {SKU_SIZE_CATEGORIES.map(size => (
            <option key={size} value={size}>
              {size}
            </option>
          ))}
        </select>
      </div>

      {/* Market Type */}
      <div className="mt-4">
        <label className={`block ${fontSize} font-medium text-gray-700 mb-1`}>
          Market Type*
        </label>
        <select
          name="market_type"
          value={formData.market_type}
          onChange={handleChange}
          required
          className={`w-full p-2 ${fontSize} border rounded-md focus:ring-blue-500 focus:border-blue-500`}
        >
          <option value="">Select Market Type</option>
          <option value="OPEN">Open Market</option>
          <option value="RETAIL">Retail</option>
          <option value="WHOLESALE">Wholesale</option>
        </select>
      </div>

      {/* Location */}
      <div className="mt-4">
        <label className={`block ${fontSize} font-medium text-gray-700 mb-1`}>
          Location*
        </label>
        <select
          name="location"
          value={formData.location}
          onChange={handleChange}
          required
          className={`w-full p-2 ${fontSize} border rounded-md focus:ring-blue-500 focus:border-blue-500`}
        >
          <option value="">Select Location</option>
          {locations.map(location => (
            <option key={location} value={location}>
              {location}
            </option>
          ))}
        </select>
      </div>

      {/* Selling Price (Case) */}
      <div className="mt-4">
        <label className={`block ${fontSize} font-medium text-gray-700 mb-1`}>
          Selling Price (Case)*
        </label>
        <input
          type="number"
          name="selling_price_case"
          value={formData.selling_price_case}
          onChange={handleChange}
          required
          min="0"
          step="0.01"
          className={`w-full p-2 ${fontSize} border rounded-md focus:ring-blue-500 focus:border-blue-500`}
        />
      </div>

      {/* Selling Price (Unit) */}
      <div className="mt-4">
        <label className={`block ${fontSize} font-medium text-gray-700 mb-1`}>
          Selling Price (Unit)*
        </label>
        <input
          type="number"
          name="selling_price_unit"
          value={formData.selling_price_unit}
          onChange={handleChange}
          required
          min="0"
          step="0.01"
          className={`w-full p-2 ${fontSize} border rounded-md focus:ring-blue-500 focus:border-blue-500`}
        />
      </div>
    </div>
  );
};

export default ProductSelectionForm;
