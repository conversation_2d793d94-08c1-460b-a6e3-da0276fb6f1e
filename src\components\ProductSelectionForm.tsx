import React, { useState, useEffect } from 'react';
import { api } from '../services/api';
import {
  SKU_CATEGORIES,
  SKU_SIZE_CATEGORIES,
  isUnileverBrand,
  getBrandFromSku,
  getSkuCategoryFromBrand,
  getSizeCategoryFromSku,
  isDeodorant,
  isSkinCare
} from '../utils/brandUtils';

// Define interfaces for form data and products
export interface ProductFormData {
  sku_name: string;
  sku_category: string;
  sku_size_category: string;
  brand: string;
  market_type: string;
  location: string;
  selling_price_case: string | number;
  selling_price_unit: string | number;
  sku_description?: string;
}

export interface Product {
  id?: string;
  sku_name: string;
  sku_category?: string;
  sku_size_category?: string;
  brand?: string;
  market_type?: string;
  location?: string;
  selling_price_case?: number;
  selling_price_unit?: number;
  created_at?: string;
  sku_description?: string;
}

interface ProductSelectionFormProps {
  formData: ProductFormData;
  onChange: (name: string, value: any) => void;
  onProductSelect: (product: Product | null) => void;
  isEditMode: boolean;
  className?: string;
  fontSize?: string;
}

const ProductSelectionForm: React.FC<ProductSelectionFormProps> = ({
  formData,
  onChange,
  onProductSelect,
  isEditMode,
  className = '',
  fontSize = 'text-[10px]'
}) => {
  // State for products
  const [allProducts, setAllProducts] = useState<Product[]>([]);
  const [competitorProducts, setCompetitorProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  // Add a separate state for display purposes
  const [displayProduct, setDisplayProduct] = useState<Product | null>(null);
  const [locations, setLocations] = useState<string[]>([]);

  // Sample data for fallback
  const SAMPLE_PRODUCTS: Product[] = [
    // DEODORANT category
    {
      id: 'sample-1',
      sku_name: 'Nivea Roll on 50ml',
      sku_category: 'DEODORANT',
      sku_size_category: 'REGULAR PACK',
      brand: 'NIVEA',
      market_type: 'RETAIL',
      location: 'Lagos',
      selling_price_case: 2400,
      selling_price_unit: 400,
      created_at: new Date().toISOString()
    },
    {
      id: 'sample-2',
      sku_name: 'Enchanter Roll on 50ml',
      sku_category: 'DEODORANT',
      sku_size_category: 'REGULAR PACK',
      brand: 'ENCHANTER',
      market_type: 'RETAIL',
      location: 'Lagos',
      selling_price_case: 1800,
      selling_price_unit: 300,
      created_at: new Date().toISOString()
    },
    {
      id: 'sample-3',
      sku_name: 'Red Diamond Roll on 50ml',
      sku_category: 'DEODORANT',
      sku_size_category: 'REGULAR PACK',
      brand: 'RED DIAMOND',
      market_type: 'RETAIL',
      location: 'Lagos',
      selling_price_case: 1500,
      selling_price_unit: 250,
      created_at: new Date().toISOString()
    },
    {
      id: 'sample-4',
      sku_name: 'Nivea Roll on 25ml',
      sku_category: 'DEODORANT',
      sku_size_category: 'SMALL PACK',
      brand: 'NIVEA',
      market_type: 'RETAIL',
      location: 'Lagos',
      selling_price_case: 1200,
      selling_price_unit: 200,
      created_at: new Date().toISOString()
    },

    // SKIN CARE category
    {
      id: 'sample-5',
      sku_name: 'Cussons Baby Jelly 50g',
      sku_category: 'SKIN CARE',
      sku_size_category: 'SMALL PACK',
      brand: 'CUSSONS',
      market_type: 'RETAIL',
      location: 'Lagos',
      selling_price_case: 1200,
      selling_price_unit: 200,
      created_at: new Date().toISOString()
    },
    {
      id: 'sample-6',
      sku_name: 'NBC Jelly 250g',
      sku_category: 'SKIN CARE',
      sku_size_category: 'REGULAR PACK',
      brand: 'NBC',
      market_type: 'RETAIL',
      location: 'Lagos',
      selling_price_case: 3000,
      selling_price_unit: 500,
      created_at: new Date().toISOString()
    },
    {
      id: 'sample-7',
      sku_name: 'Fressia Jelly 100g',
      sku_category: 'SKIN CARE',
      sku_size_category: 'REGULAR PACK',
      brand: 'FRESSIA',
      market_type: 'RETAIL',
      location: 'Lagos',
      selling_price_case: 1800,
      selling_price_unit: 300,
      created_at: new Date().toISOString()
    },

    // ORAL CARE category
    {
      id: 'sample-8',
      sku_name: 'Oral B Toothpaste 100g',
      sku_category: 'ORAL CARE',
      sku_size_category: 'REGULAR PACK',
      brand: 'ORAL B',
      market_type: 'RETAIL',
      location: 'Lagos',
      selling_price_case: 3600,
      selling_price_unit: 600,
      created_at: new Date().toISOString()
    },
    {
      id: 'sample-9',
      sku_name: 'Colgate Toothpaste 50g',
      sku_category: 'ORAL CARE',
      sku_size_category: 'SMALL PACK',
      brand: 'COLGATE',
      market_type: 'RETAIL',
      location: 'Lagos',
      selling_price_case: 1800,
      selling_price_unit: 300,
      created_at: new Date().toISOString()
    },

    // NUTRITION category
    {
      id: 'sample-10',
      sku_name: 'Maggi Cube 4g',
      sku_category: 'NUTRITION',
      sku_size_category: 'SMALL PACK',
      brand: 'MAGGI',
      market_type: 'RETAIL',
      location: 'Lagos',
      selling_price_case: 600,
      selling_price_unit: 10,
      created_at: new Date().toISOString()
    },
    {
      id: 'sample-11',
      sku_name: 'Onga Cube 4g',
      sku_category: 'NUTRITION',
      sku_size_category: 'SMALL PACK',
      brand: 'ONGA',
      market_type: 'RETAIL',
      location: 'Lagos',
      selling_price_case: 540,
      selling_price_unit: 9,
      created_at: new Date().toISOString()
    }
  ];

  // Function to fetch products and category choices
  const fetchDataInternal = async () => {
    try {
      // Fetch category choices
      const categoryData = await api.getCategoryChoices();

      // If the API returns locations, use them
      if (categoryData && categoryData.locations) {
        setLocations(categoryData.locations);
      } else {
        // Fallback to predefined locations
        setLocations(['Lagos', 'Abuja', 'Port Harcourt', 'Kano', 'Ibadan']);
      }

      // Load data from both CSV and API
      let allLoadedProducts: Product[] = [];

      // First try to load from CSV
      try {
        // Import the excelService dynamically to avoid circular dependencies
        const { excelService } = await import('../services/excelService');
        console.log('Loading data from CSV...');
        const csvProducts = await excelService.readDatabaseFiles();
        console.log('CSV data loaded:', csvProducts.length, 'products');

        // Convert CSV products to the Product format
        const formattedCsvProducts = csvProducts.map(csvProduct => ({
          id: `csv-${csvProduct.sku_name}`,
          sku_name: csvProduct.sku_name,
          sku_category: csvProduct.category,
          sku_size_category: csvProduct.sku_size,
          brand: getBrandFromSku(csvProduct.sku_name),
          market_type: 'RETAIL', // Default market type
          location: 'Lagos', // Default location
          selling_price_case: csvProduct.selling_price_case || 0,
          selling_price_unit: csvProduct.kd_unit_price || 0,
          created_at: csvProduct.uploadDate?.toISOString() || new Date().toISOString()
        }));

        allLoadedProducts = [...formattedCsvProducts];
      } catch (csvError) {
        console.error('Error loading CSV data:', csvError);
      }

      // Then try to load from API
      try {
        console.log('Fetching products from API...');
        const apiProducts = await api.getPrices();
        console.log('API products loaded:', apiProducts.length);

        // Combine with CSV products
        allLoadedProducts = [...allLoadedProducts, ...apiProducts];
      } catch (apiError) {
        console.error('Error fetching API products:', apiError);
      }

      // If we have products from either source, use them
      if (allLoadedProducts.length > 0) {
        console.log('Total products loaded:', allLoadedProducts.length);
        setAllProducts(allLoadedProducts);

        // Filter out Unilever products to get only competitor products
        const nonUnileverProducts = allLoadedProducts.filter(
          (product: Product) => !isUnileverBrand(product.sku_name)
        );
        console.log('Non-Unilever products:', nonUnileverProducts.length);
        setCompetitorProducts(nonUnileverProducts);
      } else {
        // If no products from either source, use sample data
        console.log('No products loaded, using sample data');
        setAllProducts(SAMPLE_PRODUCTS);
        setCompetitorProducts(SAMPLE_PRODUCTS);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      // Fallback to predefined locations and sample products
      setLocations(['Lagos', 'Abuja', 'Port Harcourt', 'Kano', 'Ibadan']);
      console.log('Using sample products due to API error');
      setAllProducts(SAMPLE_PRODUCTS);
      setCompetitorProducts(SAMPLE_PRODUCTS);
    }
  };

  // Use fetchDataInternal directly

  // Fetch products and category choices on component mount
  useEffect(() => {
    fetchDataInternal();
  }, []);

  // Filter products when category changes
  useEffect(() => {
    if (formData.sku_category) {
      console.log('Category selected:', formData.sku_category);
      console.log('Competitor products available:', competitorProducts.length);

      const filtered = competitorProducts.filter(product => {
        // Check if it's a deodorant and the filter is for deodorants
        if (formData.sku_category.toUpperCase() === 'DEODORANT' && isDeodorant(product.sku_name)) {
          return true;
        }

        // Check if it's a skin care product and the filter is for skin care
        if (formData.sku_category.toUpperCase() === 'SKIN CARE' && isSkinCare(product.sku_name)) {
          return true;
        }

        // If product has a category, use it
        if (product.sku_category) {
          return product.sku_category.toUpperCase() === formData.sku_category.toUpperCase();
        }

        // Otherwise try to determine category from brand or SKU name
        const brand = product.brand || getBrandFromSku(product.sku_name);
        if (brand) {
          const category = getSkuCategoryFromBrand(brand);
          return category && category.toUpperCase() === formData.sku_category.toUpperCase();
        }

        // For CSV products, check the category field
        if (product.id?.startsWith('csv-') && product.sku_category) {
          return product.sku_category.toUpperCase() === formData.sku_category.toUpperCase();
        }

        return false;
      });

      console.log('Filtered products:', filtered.length);

      // Sort filtered products by name for easier selection
      const sortedFiltered = [...filtered].sort((a, b) => {
        return a.sku_name.localeCompare(b.sku_name);
      });

      setFilteredProducts(sortedFiltered);
    } else {
      setFilteredProducts([]);
    }
  }, [formData.sku_category, competitorProducts]);

  // Handle form field changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    // If changing category, reset product selection
    if (name === 'sku_category') {
      setSelectedProduct(null);
      setDisplayProduct(null);
      onProductSelect(null);
    }

    onChange(name, value);
  };

  // Handle product selection
  const handleProductSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const productId = e.target.value;

    if (!productId) {
      setSelectedProduct(null);
      setDisplayProduct(null);
      onProductSelect(null);
      return;
    }

    // Find the product in all competitor products
    const product = competitorProducts.find(p => p.id === productId || p.sku_name === productId);

    if (product) {
      console.log('Product selected:', product);

      // Always set the display product for the dropdown
      setDisplayProduct(product);

      // Determine if this is a CSV product (create) or API product (update)
      const isFromCsv = product.id?.startsWith('csv-');
      console.log('Product source:', isFromCsv ? 'CSV (will create new)' : 'API (will update)');

      // If it's from CSV, we'll create a new entry, so don't set it as selected for update
      if (isFromCsv) {
        // For CSV products, we'll use the data but not set it as the selected product for update
        setSelectedProduct(null);
      } else {
        // For API products, we'll set it as the selected product for update
        setSelectedProduct(product);
      }

      // Determine SKU category if not present
      const skuCategory = product.sku_category ||
                         (isDeodorant(product.sku_name) ? 'DEODORANT' :
                         (isSkinCare(product.sku_name) ? 'SKIN CARE' :
                         getSkuCategoryFromBrand(product.brand || getBrandFromSku(product.sku_name) || '')));

      // Determine SKU size category if not present
      const skuSizeCategory = product.sku_size_category || getSizeCategoryFromSku(product.sku_name) || '';

      // Determine brand if not present
      const brand = product.brand || getBrandFromSku(product.sku_name) || '';

      // Update form data with selected product details
      onChange('sku_name', product.sku_name || '');
      onChange('sku_category', skuCategory || formData.sku_category || '');
      onChange('sku_size_category', skuSizeCategory || '');
      onChange('brand', brand || '');
      onChange('market_type', product.market_type || 'RETAIL');
      onChange('location', product.location || 'Lagos');

      // Handle price fields
      onChange('selling_price_case', product.selling_price_case?.toString() || '0');
      onChange('selling_price_unit', product.selling_price_unit?.toString() || '0');

      // If the product has a description, update it
      if (product.sku_description) {
        onChange('sku_description', product.sku_description);
      }

      // Notify parent component about the selected product
      // If it's from CSV, pass null as the selected product (create new)
      // If it's from API, pass the product (update existing)
      onProductSelect(isFromCsv ? null : product);

      console.log('Form data updated with selected product details');
      console.log('Edit mode:', isFromCsv ? 'No (creating new)' : 'Yes (updating existing)');
    } else {
      console.warn('Product not found with ID:', productId);
    }
  };

  return (
    <div className={className}>
      {/* SKU Category - First to filter products */}
      <div>
        <label className={`block ${fontSize} font-medium text-gray-700 mb-1`}>
          SKU Category*
        </label>
        <select
          name="sku_category"
          value={formData.sku_category}
          onChange={handleChange}
          required
          className={`w-full p-2 ${fontSize} border rounded-md focus:ring-blue-500 focus:border-blue-500`}
        >
          <option value="">Select Category</option>
          {SKU_CATEGORIES.map(category => (
            <option key={category} value={category}>
              {category}
            </option>
          ))}
        </select>
      </div>

      {/* Product Selection - Only show when category is selected */}
      {formData.sku_category && (
        <div className="mt-4">
          <div className="flex justify-between items-center mb-1">
            <label className={`block ${fontSize} font-medium text-gray-700`}>
              Select Existing Product
            </label>
            <button
              type="button"
              onClick={() => {
                fetchData();
                // Show loading message
                setFilteredProducts([{ id: 'loading', sku_name: 'Loading products...', brand: '' } as Product]);
                // After a short delay, re-filter products based on the selected category
                setTimeout(() => {
                  if (formData.sku_category) {
                    const filtered = competitorProducts.filter(product => {
                      if (formData.sku_category.toUpperCase() === 'DEODORANT' && isDeodorant(product.sku_name)) {
                        return true;
                      }
                      if (formData.sku_category.toUpperCase() === 'SKIN CARE' && isSkinCare(product.sku_name)) {
                        return true;
                      }
                      if (product.sku_category) {
                        return product.sku_category.toUpperCase() === formData.sku_category.toUpperCase();
                      }
                      const brand = product.brand || getBrandFromSku(product.sku_name);
                      if (brand) {
                        const category = getSkuCategoryFromBrand(brand);
                        return category && category.toUpperCase() === formData.sku_category.toUpperCase();
                      }
                      if (product.id?.startsWith('csv-') && product.sku_category) {
                        return product.sku_category.toUpperCase() === formData.sku_category.toUpperCase();
                      }
                      return false;
                    });
                    const sortedFiltered = [...filtered].sort((a, b) => a.sku_name.localeCompare(b.sku_name));
                    setFilteredProducts(sortedFiltered);
                  }
                }, 500);
              }}
              className={`${fontSize} text-blue-600 hover:text-blue-800 flex items-center`}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh
            </button>
          </div>
          <div className="relative">
            <select
              onChange={handleProductSelect}
              value={displayProduct?.id || displayProduct?.sku_name || ''}
              className={`w-full p-2 ${fontSize} border rounded-md focus:ring-blue-500 focus:border-blue-500`}
            >
              <option value="">-- Select a product or enter new details --</option>
              {filteredProducts.length > 0 ? (
                <>
                  {/* Check if we're showing the loading message */}
                  {filteredProducts[0].id === 'loading' ? (
                    <option value="" disabled>Loading products...</option>
                  ) : (
                    <>
                      {/* Group products by source */}
                      {filteredProducts.filter(product => product.id?.startsWith('csv-')).length > 0 && (
                        <optgroup label="CSV Products (Will Create New)">
                          {filteredProducts
                            .filter(product => product.id?.startsWith('csv-'))
                            .map((product, index) => (
                              <option key={product.id || `csv-${index}`} value={product.id || product.sku_name}>
                                {product.sku_name} - {product.brand || getBrandFromSku(product.sku_name)} (Create New)
                              </option>
                            ))}
                        </optgroup>
                      )}
                      {filteredProducts.filter(product => !product.id?.startsWith('csv-')).length > 0 && (
                        <optgroup label="Submitted Products (Will Update)">
                          {filteredProducts
                            .filter(product => !product.id?.startsWith('csv-'))
                            .map((product, index) => (
                              <option key={product.id || `api-${index}`} value={product.id || product.sku_name}>
                                {product.sku_name} - {product.brand || getBrandFromSku(product.sku_name)} (Update)
                              </option>
                            ))}
                        </optgroup>
                      )}
                    </>
                  )}
                </>
              ) : (
                <option value="" disabled>No products found in this category</option>
              )}
            </select>
          </div>
          {displayProduct ? (
            <div className={`mt-1 ${displayProduct.id?.startsWith('csv-') ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'} p-1 rounded-md`}>
              <p className={`${fontSize === 'text-[10px]' ? 'text-[9px]' : 'text-[11px]'} font-medium`}>
                {displayProduct.id?.startsWith('csv-')
                  ? 'Creating new entry based on CSV data'
                  : 'Editing existing product from database'}
              </p>
              <p className={`${fontSize === 'text-[10px]' ? 'text-[9px]' : 'text-[11px]'}`}>
                {displayProduct.id?.startsWith('csv-')
                  ? 'This will create a new entry in the database'
                  : 'This will update the existing entry in the database'}
              </p>
            </div>
          ) : (
            <p className={`${fontSize === 'text-[10px]' ? 'text-[9px]' : 'text-[11px]'} text-gray-500 mt-1`}>
              {isEditMode ? 'Editing existing product' : 'Enter new product details below or select from dropdown'}
            </p>
          )}
          {filteredProducts.length > 0 && filteredProducts[0].id !== 'loading' && (
            <p className={`${fontSize === 'text-[10px]' ? 'text-[9px]' : 'text-[11px]'} text-blue-500 mt-1`}>
              {filteredProducts.length} products available in this category
            </p>
          )}
        </div>
      )}

      {/* SKU Name */}
      <div className="mt-4">
        <label className={`block ${fontSize} font-medium text-gray-700 mb-1`}>
          SKU Name*
        </label>
        <input
          type="text"
          name="sku_name"
          value={formData.sku_name}
          onChange={handleChange}
          required
          className={`w-full p-2 ${fontSize} border rounded-md focus:ring-blue-500 focus:border-blue-500`}
        />
      </div>

      {/* Brand */}
      <div className="mt-4">
        <label className={`block ${fontSize} font-medium text-gray-700 mb-1`}>
          Brand*
        </label>
        <input
          type="text"
          name="brand"
          value={formData.brand}
          onChange={handleChange}
          required
          className={`w-full p-2 ${fontSize} border rounded-md focus:ring-blue-500 focus:border-blue-500`}
        />
      </div>

      {/* SKU Size Category */}
      <div className="mt-4">
        <label className={`block ${fontSize} font-medium text-gray-700 mb-1`}>
          SKU Size Category*
        </label>
        <select
          name="sku_size_category"
          value={formData.sku_size_category}
          onChange={handleChange}
          required
          className={`w-full p-2 ${fontSize} border rounded-md focus:ring-blue-500 focus:border-blue-500`}
        >
          <option value="">Select Size</option>
          {SKU_SIZE_CATEGORIES.map(size => (
            <option key={size} value={size}>
              {size}
            </option>
          ))}
        </select>
      </div>

      {/* Market Type */}
      <div className="mt-4">
        <label className={`block ${fontSize} font-medium text-gray-700 mb-1`}>
          Market Type*
        </label>
        <select
          name="market_type"
          value={formData.market_type}
          onChange={handleChange}
          required
          className={`w-full p-2 ${fontSize} border rounded-md focus:ring-blue-500 focus:border-blue-500`}
        >
          <option value="">Select Market Type</option>
          <option value="OPEN">Open Market</option>
          <option value="RETAIL">Retail</option>
          <option value="WHOLESALE">Wholesale</option>
        </select>
      </div>

      {/* Location */}
      <div className="mt-4">
        <label className={`block ${fontSize} font-medium text-gray-700 mb-1`}>
          Location*
        </label>
        <select
          name="location"
          value={formData.location}
          onChange={handleChange}
          required
          className={`w-full p-2 ${fontSize} border rounded-md focus:ring-blue-500 focus:border-blue-500`}
        >
          <option value="">Select Location</option>
          {locations.map(location => (
            <option key={location} value={location}>
              {location}
            </option>
          ))}
        </select>
      </div>

      {/* Selling Price (Case) */}
      <div className="mt-4">
        <label className={`block ${fontSize} font-medium text-gray-700 mb-1`}>
          Selling Price (Case)*
        </label>
        <input
          type="number"
          name="selling_price_case"
          value={formData.selling_price_case}
          onChange={handleChange}
          required
          min="0"
          step="0.01"
          className={`w-full p-2 ${fontSize} border rounded-md focus:ring-blue-500 focus:border-blue-500`}
        />
      </div>

      {/* Selling Price (Unit) */}
      <div className="mt-4">
        <label className={`block ${fontSize} font-medium text-gray-700 mb-1`}>
          Selling Price (Unit)*
        </label>
        <input
          type="number"
          name="selling_price_unit"
          value={formData.selling_price_unit}
          onChange={handleChange}
          required
          min="0"
          step="0.01"
          className={`w-full p-2 ${fontSize} border rounded-md focus:ring-blue-500 focus:border-blue-500`}
        />
      </div>
    </div>
  );
};

export default ProductSelectionForm;
