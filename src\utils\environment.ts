/**
 * Utility functions for environment-related operations
 */

/**
 * Check if the application is running in production mode
 * @returns {boolean} True if in production, false otherwise
 */
export const isProduction = (): boolean => {
  return import.meta.env.MODE === 'production';
};

/**
 * Get the API URL from environment variables
 * @returns {string} The API URL (with CORS proxy if needed)
 */
export const getApiUrl = (): string => {
  return import.meta.env.VITE_API_URL as string;
};

/**
 * Get the direct API URL without CORS proxy
 * @returns {string} The direct API URL or undefined if not set
 */
export const getDirectApiUrl = (): string | undefined => {
  return import.meta.env.VITE_API_URL_DIRECT as string;
};

/**
 * Get the environment name
 * @returns {string} 'production' or 'development'
 */
export const getEnvironmentName = (): string => {
  return isProduction() ? 'production' : 'development';
};

/**
 * Check if we should force using mock data
 * @returns {boolean} True if mock data should be forced, false otherwise
 */
export const getForceMockData = (): boolean => {
  const forceMock = import.meta.env.VITE_FORCE_MOCK_DATA;
  return forceMock === 'true' || forceMock === true;
};
