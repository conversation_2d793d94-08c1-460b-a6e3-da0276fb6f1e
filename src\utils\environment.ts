/**
 * Utility functions for environment-related operations
 */

/**
 * Check if the application is running in production mode
 * @returns {boolean} True if in production, false otherwise
 */
export const isProduction = (): boolean => {
  return import.meta.env.MODE === 'production';
};

/**
 * Get the API URL from environment variables
 * @returns {string} The API URL
 */
export const getApiUrl = (): string => {
  return import.meta.env.VITE_API_URL as string;
};

/**
 * Get the environment name
 * @returns {string} 'production' or 'development'
 */
export const getEnvironmentName = (): string => {
  return isProduction() ? 'production' : 'development';
};
