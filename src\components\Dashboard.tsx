import React, { useEffect, useState, useRef } from 'react';
import { excelService } from '../services/excelService';
import { api } from '../services/api';
import DatePicker from 'react-datepicker';
import "react-datepicker/dist/react-datepicker.css";
import PriceSubmissionForm from './PriceSubmissionForm';
import FileUpload from './FileUpload';
import Alert from './Alert';
import {
  isUnileverBrand,
  getBrandCategory,
  getBrandFromSku,
  getSkuCategoryFromBrand,
  getStandardizedSizeCategory,
  getSizeCategoryFromSku,
  isDeodorant,
  isSkinCare,
  UNILEVER_BRANDS,
  UNILEVER_BRANDS_BY_CATEGORY,
  SKU_CATEGORIES,
  SKU_SIZE_CATEGORIES
} from '../utils/brandUtils';

interface FilterOptions {
  skuCategory: string;
  skuSize: string;
  location: string;
  date: Date | null;
  marketType: string;
  brandCategory: string;
}

const MARKET_TYPES = {
  'wholesale': 'Wholesale Price',
  'openMarket': 'Open Market Price',
  'ng': 'Neighbourhood Price',
  'smallSupermarket': 'Small Supermarket Price'
};

const Dashboard: React.FC = () => {
  const [allProducts, setAllProducts] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isUsingFallback, setIsUsingFallback] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [showUploadForm, setShowUploadForm] = useState(false);
  const filterChangeTimeout = useRef<NodeJS.Timeout | null>(null);
  const [alert, setAlert] = useState<{
    show: boolean;
    type: 'success' | 'error' | 'info' | 'warning';
    message: string;
  }>({
    show: false,
    type: 'success',
    message: ''
  });

  const [filters, setFilters] = useState<FilterOptions>({
    skuCategory: '',
    skuSize: '',
    location: '',
    date: null,
    marketType: 'wholesale',
    brandCategory: ''
  });

  const [filterOptions, setFilterOptions] = useState({
    categories: new Set<string>(),
    sizes: new Set<string>(),
    locations: new Set<string>(),
    dates: new Set<Date>(),
    brandCategories: new Set<string>()
  });

  const updateFilterOptions = (products: any[]) => {
    const options = {
      categories: new Set<string>(),
      sizes: new Set<string>(),
      locations: new Set<string>(),
      dates: new Set<Date>(),
      brandCategories: new Set<string>()
    };

    // Add all SKU categories from our predefined list
    SKU_CATEGORIES.forEach(category => {
      options.categories.add(category);
    });

    // Add all SKU size categories from our predefined list
    SKU_SIZE_CATEGORIES.forEach(size => {
      options.sizes.add(size);
    });

    // We'll collect brands directly from the data
    // Add Unilever brands as a starting point
    UNILEVER_BRANDS.forEach(brand => {
      options.brandCategories.add(brand);
    });
    // Add some common competitor brands
    ['COLGATE', 'ORAL B', 'NIVEA', 'DOVE', 'MAGGI'].forEach(brand => {
      options.brandCategories.add(brand);
    });

    // Add locations, dates, categories, size categories, and brands from products
    products.forEach(item => {
      if (item.location) options.locations.add(item.location);
      if (item.created_at) options.dates.add(new Date(item.created_at));

      // Add SKU category from the item
      if (item.sku_category && !options.categories.has(item.sku_category)) {
        options.categories.add(item.sku_category);
      }

      // Add SKU size category from the item
      if (item.sku_size_category && !options.sizes.has(item.sku_size_category)) {
        options.sizes.add(item.sku_size_category);
      }

      // Add brand from the item or extract it from the SKU name
      if (item.brand) {
        options.brandCategories.add(item.brand);
      } else {
        const extractedBrand = getBrandFromSku(item.sku_name);
        if (extractedBrand && extractedBrand !== 'Unknown Brand') {
          options.brandCategories.add(extractedBrand);
        }
      }
    });

    // If no size categories were found in the data, add the predefined ones as fallback
    if (options.sizes.size === 0) {
      SKU_SIZE_CATEGORIES.forEach(size => {
        options.sizes.add(size);
      });
    }

    console.log("Size categories in filter:", [...options.sizes]);

    setFilterOptions(options);
  };

  const handleFilterChange = (filterName: keyof FilterOptions, value: any) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: value
    }));

    // Reload data with new filters after a short delay to avoid too many API calls
    // when multiple filters are changed in quick succession
    if (filterChangeTimeout.current) {
      clearTimeout(filterChangeTimeout.current);
    }

    filterChangeTimeout.current = setTimeout(() => {
      loadData();
    }, 500);
  };

  const filterData = (data: any[]) => {
    // First sort by date (newest first)
    const sortedData = [...data].sort((a, b) => {
      const dateA = new Date(a.created_at || 0);
      const dateB = new Date(b.created_at || 0);
      return dateB.getTime() - dateA.getTime();
    });

    return sortedData.filter(item => {
      const dateMatch = !filters.date ||
        new Date(item.created_at).toDateString() === filters.date.toDateString();

      const brandMatch = !filters.brandCategory ||
        (item.brand || getBrandFromSku(item.sku_name) || 'Unknown') === filters.brandCategory;

      // Size matching with case-insensitive comparison and SKU name-based fallback
      let sizeMatch = !filters.skuSize;

      if (filters.skuSize && !sizeMatch) {
        // First try direct match with item.sku_size_category
        if (item.sku_size_category && item.sku_size_category.toUpperCase() === filters.skuSize.toUpperCase()) {
          sizeMatch = true;
        }
        // Otherwise, try to determine the size category from the SKU name
        else {
          const sizeCategory = getSizeCategoryFromSku(item.sku_name);
          if (sizeCategory && sizeCategory.toUpperCase() === filters.skuSize.toUpperCase()) {
            sizeMatch = true;
          }
        }
      }

      // SKU category matching with case-insensitive comparison and brand-based fallback
      let categoryMatch = !filters.skuCategory;

      if (filters.skuCategory && !categoryMatch) {
        // Check if it's a deodorant and the filter is for deodorants
        if (filters.skuCategory.toUpperCase() === 'DEODORANT' && isDeodorant(item.sku_name)) {
          categoryMatch = true;
        }
        // Check if it's a skin care product and the filter is for skin care
        else if (filters.skuCategory.toUpperCase() === 'SKIN CARE' && isSkinCare(item.sku_name)) {
          categoryMatch = true;
        }
        // First try direct match with item.sku_category
        else if (item.sku_category && item.sku_category.toUpperCase() === filters.skuCategory.toUpperCase()) {
          categoryMatch = true;
        }
        // If no direct match, try to determine category from the brand
        else {
          const brand = item.brand || getBrandFromSku(item.sku_name);
          if (brand) {
            const category = getSkuCategoryFromBrand(brand);
            if (category && category.toUpperCase() === filters.skuCategory.toUpperCase()) {
              categoryMatch = true;
            }
          }
        }
      }

      return (
        categoryMatch &&
        sizeMatch &&
        (!filters.location || item.location === filters.location) &&
        brandMatch &&
        dateMatch
      );
    });
  };

  const getPriceForMarket = (item: any): number => {
    switch (filters.marketType) {
      case 'wholesale': return item.selling_price_case || 0;
      case 'openMarket': return item.open_market_price || 0;
      case 'ng': return item.ng_price || 0;
      case 'smallSupermarket': return item.small_supermarket_price || 0;
      default: return 0;
    }
  };

  // Get Unilever products from all products
  const getUnileverProducts = () => {
    return allProducts.filter(product => isUnileverBrand(product.sku_name || ''));
  };

  // Get competitor products from all products
  const getCompetitorProducts = () => {
    return allProducts.filter(product => !isUnileverBrand(product.sku_name || ''));
  };

  useEffect(() => {
    loadData();
  }, []);

  const handleFormSubmitSuccess = () => {
    // Show success alert
    setAlert({
      show: true,
      type: 'success',
      message: 'Price data submitted successfully!'
    });

    // Hide the form
    setShowForm(false);

    // Reload data to include the new submission
    console.log('Reloading data after form submission...');
    loadData();
  };

  const handleUploadSuccess = (message: string) => {
    // Show success alert
    setAlert({
      show: true,
      type: 'success',
      message
    });

    // Hide the upload form
    setShowUploadForm(false);

    // Reload data to include the uploaded data
    console.log('Reloading data after file upload...');
    loadData();
  };

  const handleUploadError = (message: string) => {
    // Show error alert
    setAlert({
      show: true,
      type: 'error',
      message
    });
  };

  const handleExportCSV = async () => {
    try {
      // Show loading alert
      setAlert({
        show: true,
        type: 'info',
        message: 'Exporting data to CSV...'
      });

      // Call the API to export the data
      const result = await api.exportCSV();

      // Show success alert
      setAlert({
        show: true,
        type: 'success',
        message: result.message || 'Data exported successfully!'
      });
    } catch (error) {
      console.error('Error exporting CSV:', error);

      // Show error alert
      setAlert({
        show: true,
        type: 'error',
        message: error instanceof Error ? error.message : 'Failed to export data'
      });
    }
  };

  const handleCloseAlert = () => {
    setAlert({
      ...alert,
      show: false
    });
  };

  const toggleForm = () => {
    setShowForm(!showForm);
    if (showUploadForm) setShowUploadForm(false);
  };

  const toggleUploadForm = () => {
    setShowUploadForm(!showUploadForm);
    if (showForm) setShowForm(false);
  };

  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      setIsUsingFallback(false);

      // Prepare API filters based on current filter state
      const apiFilters: any = {};
      if (filters.skuCategory) apiFilters.sku_category = filters.skuCategory;
      if (filters.skuSize) apiFilters.sku_size_category = filters.skuSize;
      if (filters.location) apiFilters.location = filters.location;
      if (filters.brandCategory) apiFilters.brand = filters.brandCategory;
      if (filters.date) {
        const formattedDate = filters.date.toISOString().split('T')[0]; // Format as YYYY-MM-DD
        apiFilters.created_at__date = formattedDate;
      }

      // Try to load data from API first
      try {
        console.log('Loading data from API with filters:', apiFilters);
        const apiProducts = await api.getPrices(apiFilters);
        console.log('API data loaded:', apiProducts.length, 'products');

        // If API call is successful, use that data
        setAllProducts(apiProducts);
        updateFilterOptions(apiProducts);
        setIsUsingFallback(false);
      } catch (apiError) {
        console.warn('API unavailable or error occurred, falling back to local data:', apiError);
        setIsUsingFallback(true);

        // Load data from CSV as fallback
        console.log('Loading data from CSV...');
        const products = await excelService.readDatabaseFiles();
        console.log('CSV data loaded:', products.length, 'products');

        // Get any uploaded Excel data
        const uploadedProducts = excelService.getUploadedFiles();
        console.log('Uploaded Excel data loaded:', uploadedProducts.length, 'products');

        // Debug: Log unique size categories in the data
        const sizeCategoriesSet = new Set<string>();
        const skuCategoriesSet = new Set<string>();
        products.forEach(product => {
          if (product.sku_size_category) {
            sizeCategoriesSet.add(product.sku_size_category);
          }
          if (product.sku_category) {
            skuCategoriesSet.add(product.sku_category);
          }
        });
        console.log('Size categories in data:', [...sizeCategoriesSet]);
        console.log('SKU categories in data:', [...skuCategoriesSet]);

        // Combine CSV and uploaded Excel data
        const combinedProducts = [...products, ...uploadedProducts];
        console.log('Total combined products (local only):', combinedProducts.length);

        // Apply filters locally since we couldn't use the API filtering
        const filteredProducts = filterData(combinedProducts);

        setAllProducts(filteredProducts);
        updateFilterOptions(combinedProducts); // Use all products for filter options
      }
    } catch (err) {
      console.error("Error in loadData:", err);
      setError(err instanceof Error ? err.message : "Failed to load data. Please try again later.");
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        {error}
      </div>
    );
  }

  const unileverProducts = getUnileverProducts();
  const competitorProducts = getCompetitorProducts();

  return (
    <div className="container mx-auto p-2">
      {/* Alert Message */}
      {alert.show && (
        <Alert
          type={alert.type}
          message={alert.message}
          onClose={handleCloseAlert}
        />
      )}

      {isUsingFallback && (
        <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4">
          Note: Using local data as backup. Some features may be limited.
        </div>
      )}

      {/* Action Buttons */}
      <div className="mb-4 flex justify-end space-x-2">
        <button
          onClick={handleExportCSV}
          className="bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
        >
          Export CSV
        </button>
        <button
          onClick={toggleUploadForm}
          className="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
        >
          {showUploadForm ? 'Cancel' : 'Upload Excel/CSV'}
        </button>
        <button
          onClick={toggleForm}
          className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          {showForm ? 'Cancel' : 'Add Price Data'}
        </button>
      </div>

      {/* File Upload Form */}
      {showUploadForm && (
        <div className="mb-4">
          <FileUpload
            onUploadSuccess={handleUploadSuccess}
            onUploadError={handleUploadError}
          />
        </div>
      )}

      {/* Price Submission Form */}
      {showForm && (
        <div className="mb-4">
          <PriceSubmissionForm onSubmitSuccess={handleFormSubmitSuccess} />
        </div>
      )}

      {/* Filters Section */}
      <div className="bg-white p-3 rounded-lg shadow-md mb-4">
        <h2 className="text-base font-semibold mb-2">Filters</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-2">
          {/* Brand Filter */}
          <div>
            <label className="block text-[10px] font-medium text-gray-700 mb-0.5">
              Brand
            </label>
            <select
              value={filters.brandCategory}
              onChange={(e) => handleFilterChange('brandCategory', e.target.value)}
              className="w-full p-1 text-[10px] border rounded-md focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Brands</option>
              {[...filterOptions.brandCategories].sort().map(category => (
                <option key={category} value={category}>
                  {category === 'OTHER' ? 'OTHER BRANDS' : category}
                </option>
              ))}
            </select>
          </div>

          {/* SKU Category Filter */}
          <div>
            <label className="block text-[10px] font-medium text-gray-700 mb-0.5">
              SKU Category
            </label>
            <select
              value={filters.skuCategory}
              onChange={(e) => handleFilterChange('skuCategory', e.target.value)}
              className="w-full p-1 text-[10px] border rounded-md focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Categories</option>
              {SKU_CATEGORIES.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>

          {/* SKU Size Filter */}
          <div>
            <label className="block text-[10px] font-medium text-gray-700 mb-0.5">
              SKU Size
            </label>
            <select
              value={filters.skuSize}
              onChange={(e) => handleFilterChange('skuSize', e.target.value)}
              className="w-full p-1 text-[10px] border rounded-md focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Sizes</option>
              {[...filterOptions.sizes].sort().map(size => (
                <option key={size} value={size}>{size}</option>
              ))}
            </select>
          </div>

          {/* Location Filter */}
          <div>
            <label className="block text-[10px] font-medium text-gray-700 mb-0.5">
              Location
            </label>
            <select
              value={filters.location}
              onChange={(e) => handleFilterChange('location', e.target.value)}
              className="w-full p-1 text-[10px] border rounded-md focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Locations</option>
              {[...filterOptions.locations].sort().map(location => (
                <option key={location} value={location}>{location}</option>
              ))}
            </select>
          </div>

          {/* Date Filter */}
          <div>
            <label className="block text-[10px] font-medium text-gray-700 mb-0.5">
              Date
            </label>
            <DatePicker
              selected={filters.date}
              onChange={(date: Date) => handleFilterChange('date', date)}
              className="w-full p-1 text-[10px] border rounded-md focus:ring-blue-500 focus:border-blue-500"
              dateFormat="yyyy-MM-dd"
              placeholderText="Select date"
              isClearable
            />
          </div>

          {/* Market Type Filter */}
          <div>
            <label className="block text-[10px] font-medium text-gray-700 mb-0.5">
              Market Type
            </label>
            <select
              value={filters.marketType}
              onChange={(e) => handleFilterChange('marketType', e.target.value)}
              className="w-full p-1 text-[10px] border rounded-md focus:ring-blue-500 focus:border-blue-500"
            >
              {Object.entries(MARKET_TYPES).map(([value, label]) => (
                <option key={value} value={value}>{label}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Data Tables */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-2">
        {/* Unilever Data Table */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <h2 className="text-base font-semibold p-2 bg-gray-50 flex justify-between items-center">
            <div>
              Unilever Prices <span className="text-xs text-gray-500">({filterData(unileverProducts).length} products)</span>
            </div>
            <div className="text-xs bg-yellow-100 text-yellow-800 px-2 py-0.5 rounded-full">
              {MARKET_TYPES[filters.marketType as keyof typeof MARKET_TYPES]}
            </div>
          </h2>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-2 py-1 text-left text-[10px] font-medium text-gray-500 uppercase tracking-wider w-1/4 whitespace-normal">SKU Name</th>
                  <th className="px-2 py-1 text-left text-[10px] font-medium text-gray-500 uppercase tracking-wider w-1/6 whitespace-normal">Brand</th>
                  <th className="px-2 py-1 text-left text-[10px] font-medium text-gray-500 uppercase tracking-wider w-1/6 whitespace-normal">SKU Category</th>
                  <th className="px-2 py-1 text-left text-[10px] font-medium text-gray-500 uppercase tracking-wider w-1/6 whitespace-normal">Size</th>
                  <th className="px-2 py-1 text-left text-[10px] font-medium text-gray-500 uppercase tracking-wider w-1/6 whitespace-normal">Price</th>
                  <th className="px-2 py-1 text-left text-[10px] font-medium text-gray-500 uppercase tracking-wider w-1/6 whitespace-normal">Date</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filterData(unileverProducts).length > 0 ? (
                  filterData(unileverProducts).map((item, index) => (
                    <tr key={index} className={index === 0 ? "bg-blue-50" : ""}>
                      <td className="px-2 py-1 text-[10px] break-words">{item.sku_name}</td>
                      <td className="px-2 py-1">
                        <span className="px-2 py-1 text-[10px] font-medium rounded-full bg-purple-100 text-purple-800">
                          {item.brand || getBrandFromSku(item.sku_name) || 'Unknown'}
                        </span>
                      </td>
                      <td className="px-2 py-1">
                        <span className="px-2 py-1 text-[10px] font-medium rounded-full bg-green-100 text-green-800">
                          {(() => {
                            // Check if it's a deodorant first
                            if (isDeodorant(item.sku_name)) {
                              return 'DEODORANT';
                            }

                            // Check if it's a skin care product
                            if (isSkinCare(item.sku_name)) {
                              return 'SKIN CARE';
                            }

                            // If the item has a SKU category, use it
                            if (item.sku_category) {
                              return item.sku_category;
                            }

                            // Otherwise, try to determine the category from the brand
                            const brand = item.brand || getBrandFromSku(item.sku_name);
                            if (brand) {
                              const category = getSkuCategoryFromBrand(brand);
                              if (category) {
                                return category;
                              }
                            }

                            return 'Unknown';
                          })()}
                        </span>
                      </td>
                      <td className="px-2 py-1">
                        <span className="px-2 py-1 text-[10px] font-medium rounded-full bg-blue-100 text-blue-800">
                          {(() => {
                            // If the item has a size category, use it
                            if (item.sku_size_category) {
                              return item.sku_size_category;
                            }

                            // Otherwise, try to determine the size category from the SKU name
                            const sizeCategory = getSizeCategoryFromSku(item.sku_name);
                            if (sizeCategory) {
                              return sizeCategory;
                            }

                            return 'Unknown';
                          })()}
                        </span>
                      </td>
                      <td className="px-2 py-1">
                        <span className="px-2 py-1 text-[10px] font-medium rounded-full bg-red-100 text-red-800">
                          ₦{getPriceForMarket(item).toLocaleString()}
                        </span>
                      </td>
                      <td className="px-2 py-1">
                        <span className="px-2 py-1 text-[10px] font-medium rounded-full bg-gray-100 text-gray-800">
                          {item.created_at ? new Date(item.created_at).toLocaleDateString() : ''}
                        </span>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={6} className="px-2 py-2 text-center text-gray-500 text-[12px]">
                      No Unilever products match the current filters
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Competitor Data Table */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <h2 className="text-base font-semibold p-2 bg-gray-50 flex justify-between items-center">
            <div>
              Competitor Prices <span className="text-xs text-gray-500">({filterData(competitorProducts).length} products)</span>
            </div>
            <div className="text-xs bg-yellow-100 text-yellow-800 px-2 py-0.5 rounded-full">
              {MARKET_TYPES[filters.marketType as keyof typeof MARKET_TYPES]}
            </div>
          </h2>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-2 py-1 text-left text-[10px] font-medium text-gray-500 uppercase tracking-wider w-1/4 whitespace-normal">SKU Name</th>
                  <th className="px-2 py-1 text-left text-[10px] font-medium text-gray-500 uppercase tracking-wider w-1/6 whitespace-normal">Brand</th>
                  <th className="px-2 py-1 text-left text-[10px] font-medium text-gray-500 uppercase tracking-wider w-1/6 whitespace-normal">SKU Category</th>
                  <th className="px-2 py-1 text-left text-[10px] font-medium text-gray-500 uppercase tracking-wider w-1/6 whitespace-normal">Size</th>
                  <th className="px-2 py-1 text-left text-[10px] font-medium text-gray-500 uppercase tracking-wider w-1/6 whitespace-normal">Price</th>
                  <th className="px-2 py-1 text-left text-[10px] font-medium text-gray-500 uppercase tracking-wider w-1/6 whitespace-normal">Date</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filterData(competitorProducts).length > 0 ? (
                  filterData(competitorProducts).map((item, index) => (
                    <tr key={index} className={index === 0 ? "bg-blue-50" : ""}>
                      <td className="px-2 py-1 text-[10px] break-words">{item.sku_name}</td>
                      <td className="px-2 py-1">
                        <span className="px-2 py-1 text-[10px] font-medium rounded-full bg-purple-100 text-purple-800">
                          {item.brand || getBrandFromSku(item.sku_name) || 'Unknown'}
                        </span>
                      </td>
                      <td className="px-2 py-1">
                        <span className="px-2 py-1 text-[10px] font-medium rounded-full bg-green-100 text-green-800">
                          {(() => {
                            // Check if it's a deodorant first
                            if (isDeodorant(item.sku_name)) {
                              return 'DEODORANT';
                            }

                            // Check if it's a skin care product
                            if (isSkinCare(item.sku_name)) {
                              return 'SKIN CARE';
                            }

                            // If the item has a SKU category, use it
                            if (item.sku_category) {
                              return item.sku_category;
                            }

                            // Otherwise, try to determine the category from the brand
                            const brand = item.brand || getBrandFromSku(item.sku_name);
                            if (brand) {
                              const category = getSkuCategoryFromBrand(brand);
                              if (category) {
                                return category;
                              }
                            }

                            return 'Unknown';
                          })()}
                        </span>
                      </td>
                      <td className="px-2 py-1">
                        <span className="px-2 py-1 text-[10px] font-medium rounded-full bg-blue-100 text-blue-800">
                          {(() => {
                            // If the item has a size category, use it
                            if (item.sku_size_category) {
                              return item.sku_size_category;
                            }

                            // Otherwise, try to determine the size category from the SKU name
                            const sizeCategory = getSizeCategoryFromSku(item.sku_name);
                            if (sizeCategory) {
                              return sizeCategory;
                            }

                            return 'Unknown';
                          })()}
                        </span>
                      </td>
                      <td className="px-2 py-1">
                        <span className="px-2 py-1 text-[10px] font-medium rounded-full bg-red-100 text-red-800">
                          ₦{getPriceForMarket(item).toLocaleString()}
                        </span>
                      </td>
                      <td className="px-2 py-1">
                        <span className="px-2 py-1 text-[10px] font-medium rounded-full bg-gray-100 text-gray-800">
                          {item.created_at ? new Date(item.created_at).toLocaleDateString() : ''}
                        </span>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={6} className="px-2 py-2 text-center text-gray-500 text-[10px]">
                      No competitor products match the current filters
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;




