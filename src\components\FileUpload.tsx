import React, { useState, useRef } from 'react';
import { excelService } from '../services/excelService';
import { api } from '../services/api';

interface FileUploadProps {
  onUploadSuccess: (message: string) => void;
  onUploadError: (message: string) => void;
}

const FileUpload: React.FC<FileUploadProps> = ({ onUploadSuccess, onUploadError }) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const file = e.dataTransfer.files[0];
      handleFile(file);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      handleFile(file);
    }
  };

  const handleFile = (file: File) => {
    // Check if file is CSV or Excel
    if (!file.name.endsWith('.csv') && !file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
      onUploadError('Please upload a CSV or Excel file');
      return;
    }

    setSelectedFile(file);
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      onUploadError('Please select a file to upload');
      return;
    }

    setIsUploading(true);

    try {
      // First, try to upload to the backend API
      try {
        console.log('Starting file upload to API...');
        const apiResult = await api.uploadCSV(selectedFile);
        console.log('API upload result:', apiResult);

        // Check if we're using mock data
        if (apiResult.message && apiResult.message.includes('MOCK DATA')) {
          console.warn('Using mock data - file not actually saved to database');

          // Process locally for display
          const result = await excelService.uploadFile(selectedFile);

          onUploadSuccess(`${apiResult.message} - ${result.length} products processed locally`);
        } else {
          // If successful with real API, also process locally for immediate display
          const result = await excelService.uploadFile(selectedFile);

          onUploadSuccess(`File uploaded successfully to database: ${apiResult.count || result.length} products loaded`);
        }
      } catch (apiError: any) {
        console.error('Error uploading to API, falling back to local processing:', apiError);

        // Show more detailed error information
        let errorMessage = 'API upload failed';
        if (apiError.response) {
          errorMessage += `: ${apiError.response.status} - ${JSON.stringify(apiError.response.data)}`;
        } else if (apiError.message) {
          errorMessage += `: ${apiError.message}`;
        }
        console.error(errorMessage);

        // If API upload fails, still process locally
        const result = await excelService.uploadFile(selectedFile);
        onUploadSuccess(`File processed locally: ${result.length} products loaded (${errorMessage})`);
      }

      setSelectedFile(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      console.error('Error uploading file:', error);
      onUploadError(error instanceof Error ? error.message : 'Failed to upload file');
    } finally {
      setIsUploading(false);
    }
  };

  const handleGenerateTemplate = async () => {
    try {
      await excelService.generateTemplate();
      onUploadSuccess('Template generated successfully. Check your downloads folder.');
    } catch (error) {
      console.error('Error generating template:', error);
      onUploadError(error instanceof Error ? error.message : 'Failed to generate template');
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-xl font-semibold mb-4">Upload Price Data</h2>

      <div
        className={`border-2 border-dashed p-6 rounded-lg text-center mb-4 ${
          isDragging ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <div className="flex flex-col items-center justify-center">
          <svg
            className="w-12 h-12 text-gray-400 mb-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
            />
          </svg>

          <p className="text-sm text-gray-600 mb-2">
            Drag & drop your CSV or Excel file here, or
          </p>

          <label className="cursor-pointer bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
            Browse Files
            <input
              type="file"
              className="hidden"
              accept=".csv,.xlsx,.xls"
              onChange={handleFileChange}
              ref={fileInputRef}
            />
          </label>

          {selectedFile && (
            <div className="mt-2 text-sm text-gray-600">
              Selected: {selectedFile.name}
            </div>
          )}
        </div>
      </div>

      <div className="flex flex-col sm:flex-row gap-2">
        <button
          onClick={handleUpload}
          disabled={!selectedFile || isUploading}
          className={`py-2 px-4 rounded-md text-white font-medium ${
            !selectedFile || isUploading
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2'
          }`}
        >
          {isUploading ? 'Uploading...' : 'Upload File'}
        </button>

        <button
          onClick={handleGenerateTemplate}
          className="py-2 px-4 rounded-md text-white font-medium bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
        >
          Generate Template
        </button>
      </div>

      <div className="mt-4 text-xs text-gray-500">
        <p>Supported file formats: CSV, Excel (.xlsx, .xls)</p>
        <p>Maximum file size: 10MB</p>
      </div>
    </div>
  );
};

export default FileUpload;
