# Development environment variables
# Using production backend API with CORS proxy
VITE_API_URL=https://corsproxy.io/?https://price-pickup-backend.onrender.com/api

# Direct API URL (without CORS proxy) - Will be tried first before falling back to proxy
VITE_API_URL_DIRECT=https://price-pickup-backend.onrender.com/api

# Set to 'false' to use the real API when available
# Set to 'true' to force using mock data for testing
VITE_FORCE_MOCK_DATA=false
