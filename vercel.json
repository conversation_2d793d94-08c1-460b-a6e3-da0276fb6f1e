{"version": 2, "builds": [{"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": "dist", "buildCommand": "npm run build"}}], "routes": [{"src": "/database/(.*)", "dest": "/database/$1"}, {"src": "/assets/(.*)", "headers": {"cache-control": "public, max-age=31536000, immutable"}, "continue": true}, {"handle": "filesystem"}, {"src": "/(.*)", "dest": "/index.html"}], "env": {"VITE_API_URL": "https://price-pickup-backend.onrender.com/api"}}