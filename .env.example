# Backend API URL - Update this to match your Django backend server
# For local development:
# VITE_API_URL=http://localhost:8000/api

# For production with CORS proxy (use this if you encounter CORS errors):
VITE_API_URL=https://corsproxy.io/?https://price-pickup-backend.onrender.com/api

# Direct API URL (without CORS proxy) - Will be tried first before falling back to proxy
# The app will try to connect directly to the backend first, and only use the CORS proxy if needed
VITE_API_URL_DIRECT=https://price-pickup-backend.onrender.com/api

# Set to 'true' to force using mock data even if API is available (for testing)
# Set to 'false' to use the real API when available
VITE_FORCE_MOCK_DATA=false