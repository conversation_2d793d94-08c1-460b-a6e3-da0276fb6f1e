# Backend API URL - Update this to match your Django backend server
# For local development:
# VITE_API_URL=http://localhost:8000/api

# For production with CORS proxy (use this if you encounter CORS errors):
VITE_API_URL=https://corsproxy.io/?https://price-pickup-backend.onrender.com/api

# For production without CORS proxy (use this if CORS is properly configured on the backend):
# VITE_API_URL=https://price-pickup-backend.onrender.com/api

# Set to 'true' to force using mock data even if API is available (for testing)
# Set to 'false' to use the real API when available
VITE_FORCE_MOCK_DATA=false