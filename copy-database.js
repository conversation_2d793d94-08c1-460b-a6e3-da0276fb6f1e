// CommonJS module
const fs = require('fs');
const path = require('path');

// Log the current directory for debugging
console.log('Current directory:', __dirname);
console.log('Files in current directory:', fs.readdirSync(__dirname));

// Ensure the database directory exists in the dist folder
const databaseDir = path.join(__dirname, 'dist', 'database');
console.log('Creating database directory at:', databaseDir);

if (!fs.existsSync(databaseDir)) {
  try {
    fs.mkdirSync(databaseDir, { recursive: true });
    console.log('Database directory created successfully');
  } catch (err) {
    console.error('Error creating database directory:', err);
  }
}

// Copy the CSV file from src/database to dist/database
const srcFile = path.join(__dirname, 'src', 'database', 'price-pickup.csv');
const destFile = path.join(databaseDir, 'price-pickup.csv');
console.log('Source file path:', srcFile);
console.log('Destination file path:', destFile);

try {
  if (fs.existsSync(srcFile)) {
    fs.copyFileSync(srcFile, destFile);
    console.log('CSV file copied successfully to dist/database');
  } else {
    console.warn('Source CSV file not found at', srcFile);

    // Create a simple fallback CSV if the source file doesn't exist
    const fallbackContent =
`SKU Category,SKU Size,SKU Name,Brand,,,KD Case Price,KD Unit Price,KD Price/Gram,,,Selling Price Case,,,,,Open Market Price,,,,,NG Price,,,,,Small Supermarket Price
DEODORANT,REGULAR PACK,Example Product,BRAND,,,1000,200,10,,,1200,,,,,1500,,,,,1400,,,,,1600
`;

    fs.writeFileSync(destFile, fallbackContent);
    console.log('Created fallback CSV file in dist/database');
  }
} catch (err) {
  console.error('Error handling CSV file:', err);

  // Try to create the fallback file even if there was an error
  try {
    const fallbackContent =
`SKU Category,SKU Size,SKU Name,Brand,,,KD Case Price,KD Unit Price,KD Price/Gram,,,Selling Price Case,,,,,Open Market Price,,,,,NG Price,,,,,Small Supermarket Price
DEODORANT,REGULAR PACK,Example Product,BRAND,,,1000,200,10,,,1200,,,,,1500,,,,,1400,,,,,1600
`;

    fs.writeFileSync(destFile, fallbackContent);
    console.log('Created fallback CSV file after error');
  } catch (fallbackErr) {
    console.error('Failed to create fallback CSV:', fallbackErr);
  }
}

// Create a simple index.html file in the database directory to prevent 404s
try {
  const indexContent = `
<!DOCTYPE html>
<html>
<head>
  <title>Price Pickup Database</title>
</head>
<body>
  <h1>Price Pickup Database</h1>
  <p>This directory contains data files for the Price Pickup application.</p>
  <p><a href="price-pickup.csv">Download CSV</a></p>
</body>
</html>
`;

  fs.writeFileSync(path.join(databaseDir, 'index.html'), indexContent);
  console.log('Created index.html in database directory');
} catch (indexErr) {
  console.error('Error creating index.html:', indexErr);
}




