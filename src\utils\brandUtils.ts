// List of Unilever brands
export const UNILEVER_BRANDS = [
  '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>',
  'PEARS',  // Changed from BEARS to PEARS
  'PEPSODENT',
  'REXON<PERSON>',
  'ROY<PERSON>',
  'VASELINE',  // Corrected spelling from VASLINE to VASELINE
  'ANNAPURNA'  // Corrected spelling from ANNAPURMA to ANNAPURNA
];

// Brand categories mapping - these are the actual brands, not categories
export const UNILEVER_BRANDS_BY_CATEGORY = {
  'ORAL CARE': ['PEPSODENT', 'CLOSEUP'],
  'SKIN CARE': ['VASELINE', 'PEARS'],
  'NUTRITION': ['KNORR', 'ROY<PERSON>', 'ANNAPURNA'],
  'DEODORANT': ['REXONA']
};

// SKU categories (from CSV file A5 column)
export const SKU_CATEGORIES = ['DEODORANT', 'NUTRITION', 'ORAL CARE', 'SKIN CARE'];

// SKU size categories (from CSV file B5 column)
export const SKU_SIZE_CATEGORIES = ['BUL<PERSON> PACK', 'MID PACK', 'POWERS', 'REGULAR PACK', 'SMALL PACK'];

/**
 * Checks if a product belongs to Unilever based on its SKU name
 * @param skuName The SKU name to check
 * @returns True if the product is a Unilever brand
 */
export const isUnileverBrand = (skuName: string): boolean => {
  if (!skuName) return false;

  // Convert to uppercase for case-insensitive comparison
  const upperSkuName = skuName.toUpperCase();

  // Special case for specific products
  if (upperSkuName.includes('ANNAPURNA CHICKEN') ||
      (upperSkuName.includes('ANNAPURNA') && upperSkuName.includes('CHICKEN'))) {
    return true;
  }

  // Check if any Unilever brand name is contained in the SKU name
  return UNILEVER_BRANDS.some(brand => upperSkuName.includes(brand));
};

/**
 * Gets the SKU category for a product based on its SKU name
 * @param skuName The SKU name to check
 * @returns The SKU category or undefined if not found
 */
export const getBrandCategory = (skuName: string): string | undefined => {
  if (!skuName) return undefined;

  // Convert to uppercase for case-insensitive comparison
  const upperSkuName = skuName.toUpperCase();

  // Special case for specific products
  if (upperSkuName.includes('ANNAPURNA CHICKEN') ||
      (upperSkuName.includes('ANNAPURNA') && upperSkuName.includes('CHICKEN'))) {
    return 'NUTRITION';
  }

  // Find the category that contains this brand
  for (const [category, brands] of Object.entries(UNILEVER_BRANDS_BY_CATEGORY)) {
    if (brands.some((brand: string) => upperSkuName.includes(brand))) {
      return category;
    }
  }

  // Check for deodorant products
  if (isDeodorant(skuName)) {
    return 'DEODORANT';
  }

  // Check for skin care products
  if (isSkinCare(skuName)) {
    return 'SKIN CARE';
  }

  // Check for competitor brands and map them to the same categories
  if (upperSkuName.includes('COLGATE') || upperSkuName.includes('ORAL B')) {
    return 'ORAL CARE';
  } else if (upperSkuName.includes('NIVEA') && !upperSkuName.includes('ROLL')) {
    return 'SKIN CARE';
  } else if (upperSkuName.includes('DOVE')) {
    return 'SKIN CARE';
  } else if (upperSkuName.includes('MAGGI')) {
    return 'NUTRITION';
  }

  return 'OTHER';
};

/**
 * Gets the SKU category for a product based on its brand name
 * @param brandName The brand name to check
 * @returns The SKU category or undefined if not found
 */
export const getSkuCategoryFromBrand = (brandName: string): string | undefined => {
  if (!brandName) return undefined;

  // Convert to uppercase for case-insensitive comparison
  const upperBrandName = brandName.toUpperCase().trim();

  // Special case for specific brands
  if (upperBrandName === 'ANNAPURNA' || upperBrandName.includes('ANNAPURNA CHICKEN')) {
    return 'NUTRITION';
  }

  // Find the category that contains this brand
  for (const [category, brands] of Object.entries(UNILEVER_BRANDS_BY_CATEGORY)) {
    if (brands.some((brand: string) => brand.toUpperCase() === upperBrandName)) {
      return category;
    }
  }

  // Check for deodorant brands
  if (upperBrandName === 'REXONA' ||
      upperBrandName === 'ENCHANTER' ||
      upperBrandName === 'RED DIAMOND' ||
      upperBrandName.includes('ROLL ON')) {
    return 'DEODORANT';
  }

  // Check for skin care brands
  if (upperBrandName === 'VASELINE' ||
      upperBrandName === 'PEARS' ||
      upperBrandName === 'CUSSONS' ||
      upperBrandName === 'NBC' ||
      upperBrandName === 'FRESSIA' ||
      upperBrandName.includes('JELLY') ||
      upperBrandName.includes('BABY OIL') ||
      upperBrandName.includes('BABY LOTION')) {
    return 'SKIN CARE';
  }

  // Check for competitor brands and map them to the same categories
  if (upperBrandName === 'COLGATE' || upperBrandName === 'ORAL B') {
    return 'ORAL CARE';
  } else if (upperBrandName === 'NIVEA') {
    // For Nivea, we need to check the full SKU name to determine if it's a deodorant
    // This will be handled in the getBrandCategory function
    return 'SKIN CARE';
  } else if (upperBrandName === 'DOVE') {
    return 'SKIN CARE';
  } else if (upperBrandName === 'MAGGI') {
    return 'NUTRITION';
  }

  return 'OTHER';
};

/**
 * Checks if a product is a deodorant based on its SKU name
 * @param skuName The SKU name to check
 * @returns True if the product is a deodorant
 */
export const isDeodorant = (skuName: string): boolean => {
  if (!skuName) return false;

  // Convert to uppercase for case-insensitive comparison
  const upperSkuName = skuName.toUpperCase();

  // Check for common deodorant indicators
  return upperSkuName.includes('ROLL ON') ||
         upperSkuName.includes('REXONA') ||
         (upperSkuName.includes('NIVEA') && upperSkuName.includes('ROLL')) ||
         upperSkuName.includes('ENCHANTER ROLL') ||
         upperSkuName.includes('RED DIAMOND ROLL') ||
         (upperSkuName.includes('DEODORANT'));
};

/**
 * Checks if a product is a skin care product based on its SKU name
 * @param skuName The SKU name to check
 * @returns True if the product is a skin care product
 */
export const isSkinCare = (skuName: string): boolean => {
  if (!skuName) return false;

  // Convert to uppercase for case-insensitive comparison
  const upperSkuName = skuName.toUpperCase();

  // Check for specific skin care products
  if (upperSkuName.includes('VASELINE') ||
      upperSkuName.includes('PEARS BABY') ||
      upperSkuName.includes('CUSSONS') ||
      upperSkuName.includes('NBC JELLY') ||
      upperSkuName.includes('NBC 105ML') ||
      upperSkuName.includes('FRESSIA JELLY')) {
    return true;
  }

  // Check for common skin care indicators
  return upperSkuName.includes('JELLY') ||
         upperSkuName.includes('BABY OIL') ||
         upperSkuName.includes('BABY LOTION') ||
         upperSkuName.includes('BLUESEAL') ||
         (upperSkuName.includes('SKIN') && upperSkuName.includes('CARE'));
};

/**
 * Extracts the brand name from the SKU name
 * @param skuName The SKU name to extract brand from
 * @returns The brand name or undefined if not found
 */
export const getBrandFromSku = (skuName: string): string | undefined => {
  if (!skuName) return undefined;

  // Convert to uppercase for case-insensitive comparison
  const upperSkuName = skuName.toUpperCase();

  // Special case for specific products
  if (upperSkuName.includes('ANNAPURNA CHICKEN') ||
      upperSkuName.includes('ANNAPURNA') && upperSkuName.includes('CHICKEN')) {
    return 'ANNAPURNA';
  }

  // Check for Unilever brands
  for (const brand of UNILEVER_BRANDS) {
    if (upperSkuName.includes(brand)) {
      return brand;
    }
  }

  // Check for common competitor brands
  const competitorBrands = ['COLGATE', 'ORAL B', 'NIVEA', 'DOVE', 'MAGGI'];
  for (const brand of competitorBrands) {
    if (upperSkuName.includes(brand)) {
      return brand;
    }
  }

  // Try to extract the first word as the brand
  const firstWord = skuName.split(' ')[0];
  if (firstWord && firstWord.length > 2) {
    return firstWord;
  }

  return 'Unknown Brand';
};

/**
 * Separates products into Unilever and competitor products
 * @param products Array of product data
 * @returns Object with separated arrays for Unilever and competitor products
 */
export const separateProductsByBrand = (products: any[]): { unileverProducts: any[], competitorProducts: any[] } => {
  const unileverProducts: any[] = [];
  const competitorProducts: any[] = [];

  products.forEach(product => {
    if (isUnileverBrand(product.sku_name)) {
      unileverProducts.push(product);
    } else {
      competitorProducts.push(product);
    }
  });

  return { unileverProducts, competitorProducts };
};

/**
 * Determines the size category based on the SKU name and pattern
 * @param skuName The SKU name to analyze
 * @returns The size category based on the SKU pattern
 */
export const getSizeCategoryFromSku = (skuName: string): string => {
  if (!skuName) return '';

  // Convert to uppercase for case-insensitive comparison
  const upperSkuName = skuName.toUpperCase().trim();

  // Check for BULK PACK patterns
  if (upperSkuName.includes('BULK') ||
      upperSkuName.match(/\d+X\d+KG/) || // e.g., 3X2KG
      upperSkuName.match(/\d+X\d+X\d+G/) && upperSkuName.includes('BULK')) { // e.g., 5X500X4G BULK
    return 'BULK PACK';
  }

  // Check for POWERS patterns (powder products)
  if (upperSkuName.includes('POWDER') ||
      upperSkuName.match(/\d+G POWDER/) || // e.g., 800G POWDER
      upperSkuName.match(/\d+G$/) && parseInt(upperSkuName.match(/(\d+)G/)?.[1] || '0') >= 250) { // e.g., 400G, 800G
    return 'POWERS';
  }

  // Check for SMALL PACK patterns
  if ((upperSkuName.match(/\d+G BY \d+ X \d+/) || upperSkuName.match(/\d+G BY\d+X\d+/)) &&
      (upperSkuName.includes('BY 9 X') ||
       upperSkuName.includes('BY 10 X') ||
       upperSkuName.includes('BY 16 X') ||
       upperSkuName.includes('BY 20 X') ||
       upperSkuName.includes('BY 22 X') ||
       upperSkuName.includes('BY 24 X'))) {
    return 'SMALL PACK';
  }

  // Check for specific SMALL PACK products
  if (upperSkuName.includes('ANNAPURNA CHICKEN 4G BY 22 X 30')) {
    return 'SMALL PACK';
  }

  // Check for MID PACK patterns
  if ((upperSkuName.match(/\d+G BY \d+ X \d+/) || upperSkuName.match(/\d+G BY\d+X\d+/)) &&
      (upperSkuName.includes('BY 15 X') ||
       upperSkuName.includes('BY 25 X') ||
       upperSkuName.includes('BY 48 X 24') ||
       upperSkuName.includes('BY 50 X 20') ||
       upperSkuName.includes('BY 50 X 24'))) {
    return 'MID PACK';
  }

  // Check for REGULAR PACK patterns
  if ((upperSkuName.match(/\d+G BY \d+ X \d+/) || upperSkuName.match(/\d+G BY\d+X\d+/)) &&
      (upperSkuName.includes('BY 80 X 20') ||
       upperSkuName.includes('BY 100 X') ||
       upperSkuName.includes('BY 105 X'))) {
    return 'REGULAR PACK';
  }

  // Check for specific REGULAR PACK products
  if (upperSkuName.includes('KNORR BEEF 8G BY 14 X 50') ||
      upperSkuName.includes('KNORR CHICKEN 8G BY 48 X 14')) {
    return 'REGULAR PACK';
  }

  // Default fallback based on general patterns
  if (upperSkuName.includes('BULK')) return 'BULK PACK';
  if (upperSkuName.includes('POWDER')) return 'POWERS';
  if (upperSkuName.match(/\d+G$/) && parseInt(upperSkuName.match(/(\d+)G/)?.[1] || '0') >= 250) return 'POWERS';
  if (upperSkuName.includes('BY 100 X')) return 'REGULAR PACK';
  if (upperSkuName.includes('BY 20 X') || upperSkuName.includes('BY 24 X')) return 'SMALL PACK';
  if (upperSkuName.includes('BY 25 X') || upperSkuName.includes('BY 50 X')) return 'MID PACK';

  return '';
};

/**
 * Gets the size category for a product
 * @param skuSizeCategory The SKU size category from the product
 * @returns The standardized size category
 */
export const getStandardizedSizeCategory = (skuSizeCategory: string): string => {
  // If the category is undefined or null, return a default value
  if (!skuSizeCategory) return '';

  // Convert to uppercase for case-insensitive comparison and remove extra spaces
  const upperSizeCategory = skuSizeCategory.toUpperCase().trim();

  // Direct mapping for exact matches
  for (const category of SKU_SIZE_CATEGORIES) {
    if (category === upperSizeCategory) {
      return category;
    }
  }

  // Case-insensitive matching
  for (const category of SKU_SIZE_CATEGORIES) {
    if (category.toUpperCase() === upperSizeCategory) {
      return category;
    }
  }

  // Fuzzy matching for partial matches
  if (upperSizeCategory.includes('BULK')) return 'BULK PACK';
  if (upperSizeCategory.includes('MID')) return 'MID PACK';
  if (upperSizeCategory.includes('POWER')) return 'POWERS';
  if (upperSizeCategory.includes('REGULAR')) return 'REGULAR PACK';
  if (upperSizeCategory.includes('SMALL')) return 'SMALL PACK';

  // If no match is found, return the original value
  return skuSizeCategory;
};

/**
 * Groups products by their brand category
 * @param products Array of product data
 * @returns Object with products grouped by brand category
 */
export const groupProductsByCategory = (products: any[]): Record<string, any[]> => {
  const groupedProducts: Record<string, any[]> = {};

  products.forEach(product => {
    const category = getBrandCategory(product.sku_name) || 'OTHER';

    if (!groupedProducts[category]) {
      groupedProducts[category] = [];
    }

    groupedProducts[category].push(product);
  });

  return groupedProducts;
};