import React from 'react';

// Define the available SKU categories with icons and colors
const SKU_CATEGORIES = [
  { id: 'NUTRITION', name: 'NUTRITION', icon: '🍽️', color: 'bg-blue-500' },
  { id: 'ORAL CARE', name: 'ORAL CARE', icon: '🦷', color: 'bg-green-500' },
  { id: 'DEODORANT', name: 'DEODORANT', icon: '💨', color: 'bg-purple-500' },
  { id: 'SKIN CARE', name: 'SKIN CARE', icon: '🧴', color: 'bg-pink-500' },
  { id: 'SALVORY', name: 'SALVORY', icon: '🧂', color: 'bg-yellow-500' }
];

interface NewSKUCategorySelectionProps {
  onCategorySelect: (category: string) => void;
  onBack: () => void;
  fontSize?: string;
}

const NewSKUCategorySelection: React.FC<NewSKUCategorySelectionProps> = ({
  onCategorySelect,
  onBack,
  fontSize = 'text-sm'
}) => {
  return (
    <div className="max-w-md mx-auto bg-white min-h-screen">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-500 to-blue-600 text-white p-4 rounded-b-lg shadow-lg">
        <div className="flex items-center mb-2">
          <button onClick={onBack} className="mr-3 text-white hover:bg-white/20 p-1 rounded">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <div>
            <h1 className={`${fontSize === 'text-sm' ? 'text-lg' : fontSize} font-bold`}>Create New SKU</h1>
            <p className="text-blue-100 text-sm">Step 1: Select Category</p>
          </div>
        </div>
      </div>

      {/* Category Selection */}
      <div className="p-4">
        <div className="mb-4">
          <h2 className={`${fontSize === 'text-sm' ? 'text-base' : fontSize} font-semibold text-gray-800 mb-2`}>
            Choose Product Category
          </h2>
          <p className="text-gray-600 text-sm">Select the category for your new product</p>
        </div>

        {/* Category Buttons */}
        <div className="space-y-3">
          {SKU_CATEGORIES.map((category, index) => (
            <button
              key={category.id}
              onClick={() => onCategorySelect(category.id)}
              className="w-full flex items-center justify-between p-4 rounded-xl shadow-sm border border-gray-200 hover:bg-gray-50 hover:shadow-md transition-all duration-200 transform hover:scale-[1.02]"
            >
              <div className="flex items-center space-x-4">
                {/* Number Badge */}
                <div className={`w-8 h-8 rounded-full ${category.color} flex items-center justify-center text-white font-bold text-sm shadow-sm`}>
                  {index + 1}
                </div>
                
                {/* Category Info */}
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{category.icon}</span>
                  <span className={`${fontSize === 'text-sm' ? 'text-sm' : fontSize} font-medium text-gray-700`}>
                    {category.name}
                  </span>
                </div>
              </div>

              {/* Arrow */}
              <div className="text-gray-400">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </button>
          ))}
        </div>

        {/* Info Box */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-3 mt-6">
          <div className="flex items-start">
            <svg className="w-5 h-5 text-green-500 mt-0.5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div>
              <p className="text-green-800 text-xs font-medium">Creating New Product</p>
              <p className="text-green-700 text-xs mt-1">
                After selecting a category, you'll choose the market type and enter product details.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NewSKUCategorySelection;
