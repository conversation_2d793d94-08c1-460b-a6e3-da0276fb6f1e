import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import PricePickupForm from './components/PricePickupForm';
import Dashboard from './components/Dashboard';
import Navigation from './components/Navigation';
import { isProduction, getEnvironmentName, getApiUrl } from './utils/environment';

const App: React.FC = () => {
  return (
    <Router>
      <div className="max-w-7xl mx-auto py-4 px-2 sm:px-4">
        <h1 className="text-2xl sm:text-3xl font-bold mb-4">📊 Price Pickup App</h1>

        {/* Show environment info in development mode */}
        {!isProduction() && (
          <div className="bg-blue-100 border-l-4 border-blue-500 text-blue-700 p-2 mb-4 text-sm">
            <p><strong>Environment:</strong> {getEnvironmentName()}</p>
            <p><strong>API URL:</strong> {getApiUrl()}</p>
          </div>
        )}

        <Navigation />
        <Routes>
          {/* Redirect root to price-pickup form */}
          <Route path="/" element={<Navigate to="/price-pickup" replace />} />

          {/* Main routes */}
          <Route path="/price-pickup" element={<PricePickupForm />} />
          <Route path="/dashboard" element={<Dashboard />} />
        </Routes>
      </div>
    </Router>
  );
};

export default App;

