import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import PricePickupForm from './components/PricePickupForm';
import Dashboard from './components/Dashboard';
import { isProduction, getEnvironmentName, getApiUrl } from './utils/environment';

const App: React.FC = () => {
  return (
    <Router>
      <Routes>
        {/* Default route - Price Pickup Form only */}
        <Route path="/" element={
          <div className="min-h-screen bg-gray-50">
            {/* Show environment info in development mode */}
            {!isProduction() && (
              <div className="bg-blue-100 border-l-4 border-blue-500 text-blue-700 p-2 text-sm">
                <p><strong>Environment:</strong> {getEnvironmentName()}</p>
                <p><strong>API URL:</strong> {getApiUrl()}</p>
              </div>
            )}
            <PricePickupForm />
          </div>
        } />

        {/* Dashboard route - accessible only via LT_DASHBOARD */}
        <Route path="/LT_DASHBOARD" element={
          <div className="max-w-7xl mx-auto py-4 px-2 sm:px-4">
            <div className="flex items-center justify-between mb-6">
              <h1 className="text-2xl sm:text-3xl font-bold">📊 Price Pickup Dashboard</h1>
              <a
                href="/"
                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
              >
                Back to Form
              </a>
            </div>

            {/* Show environment info in development mode */}
            {!isProduction() && (
              <div className="bg-blue-100 border-l-4 border-blue-500 text-blue-700 p-2 mb-4 text-sm">
                <p><strong>Environment:</strong> {getEnvironmentName()}</p>
                <p><strong>API URL:</strong> {getApiUrl()}</p>
              </div>
            )}

            <Dashboard />
          </div>
        } />

        {/* Redirect any other routes to home */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </Router>
  );
};

export default App;

