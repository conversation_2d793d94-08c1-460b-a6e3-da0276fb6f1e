import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

export default defineConfig({
  plugins: [react()],
  assetsInclude: ['**/*.xlsx', '**/*.csv'], // This ensures Excel and CSV files are treated as assets
  build: {
    rollupOptions: {
      input: {
        main: './index.html',
      },
    },
    copyPublicDir: true,
    // Ensure output directory is properly set for Vercel
    outDir: 'dist',
  },
  // Set the public directory
  publicDir: 'public',
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  // Add base path for Vercel deployment
  base: './',
})


