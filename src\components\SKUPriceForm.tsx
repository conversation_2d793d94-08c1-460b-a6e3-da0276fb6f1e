import React, { useState, useEffect } from 'react';
import { api } from '../services/api';
import { ProductFormData, Product } from './ProductSelectionForm';

interface SKUPriceFormProps {
  selectedCategory: string;
  onBack: () => void;
  onSubmit: (data: ProductFormData) => void;
  fontSize?: string;
}

// Market type options
const MARKET_TYPES = [
  { value: 'OPEN_MARKET', label: 'Open Market' },
  { value: 'NG', label: 'NG' },
  { value: 'SMALL_SUPERMARKET', label: 'Small Supermarket' },
  { value: 'WHOLESALE', label: 'Wholesale' }
];

const SKUPriceForm: React.FC<SKUPriceFormProps> = ({
  selectedCategory,
  onBack,
  onSubmit,
  fontSize = 'text-sm'
}) => {
  const [availableProducts, setAvailableProducts] = useState<Product[]>([]);
  const [selectedMarketType, setSelectedMarketType] = useState<string>('');
  const [priceEntries, setPriceEntries] = useState<{ [key: string]: string }>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [allProducts, setAllProducts] = useState<Product[]>([]);

  const PRODUCTS_PER_PAGE = 10;

  // Sample products for each category (including both Unilever and competitors)
  const SAMPLE_PRODUCTS: { [key: string]: Product[] } = {
    'NUTRITION': [
      { id: 'sample-1', sku_name: 'Knorr Cube 4g', brand: 'KNORR', sku_category: 'NUTRITION', sku_size: 'SMALL PACK', is_unilever: true, kd_unit: 10 },
      { id: 'sample-2', sku_name: 'Maggi Cube 4g', brand: 'MAGGI', sku_category: 'NUTRITION', sku_size: 'SMALL PACK', is_unilever: false, kd_unit: 9 },
      { id: 'sample-3', sku_name: 'Onga Cube 4g', brand: 'ONGA', sku_category: 'NUTRITION', sku_size: 'SMALL PACK', is_unilever: false, kd_unit: 8 },
      { id: 'sample-4', sku_name: 'Royco Cube 4g', brand: 'ROYCO', sku_category: 'NUTRITION', sku_size: 'SMALL PACK', is_unilever: true, kd_unit: 11 }
    ],
    'ORAL CARE': [
      { id: 'sample-5', sku_name: 'Close Up Toothpaste 100g', brand: 'CLOSE UP', sku_category: 'ORAL CARE', sku_size: 'REGULAR PACK', is_unilever: true, kd_unit: 600 },
      { id: 'sample-6', sku_name: 'Pepsodent Toothpaste 100g', brand: 'PEPSODENT', sku_category: 'ORAL CARE', sku_size: 'REGULAR PACK', is_unilever: true, kd_unit: 550 },
      { id: 'sample-7', sku_name: 'Colgate Toothpaste 100g', brand: 'COLGATE', sku_category: 'ORAL CARE', sku_size: 'REGULAR PACK', is_unilever: false, kd_unit: 580 },
      { id: 'sample-8', sku_name: 'Oral B Toothpaste 100g', brand: 'ORAL B', sku_category: 'ORAL CARE', sku_size: 'REGULAR PACK', is_unilever: false, kd_unit: 620 }
    ],
    'DEODORANT': [
      { id: 'sample-9', sku_name: 'Rexona Spray 150ml', brand: 'REXONA', sku_category: 'DEODORANT', sku_size: 'REGULAR PACK', is_unilever: true, kd_unit: 800 },
      { id: 'sample-10', sku_name: 'Sure Spray 150ml', brand: 'SURE', sku_category: 'DEODORANT', sku_size: 'REGULAR PACK', is_unilever: true, kd_unit: 750 },
      { id: 'sample-11', sku_name: 'Nivea Roll on 50ml', brand: 'NIVEA', sku_category: 'DEODORANT', sku_size: 'REGULAR PACK', is_unilever: false, kd_unit: 400 },
      { id: 'sample-12', sku_name: 'Enchanter Roll on 50ml', brand: 'ENCHANTER', sku_category: 'DEODORANT', sku_size: 'REGULAR PACK', is_unilever: false, kd_unit: 300 },
      { id: 'sample-13', sku_name: 'Red Diamond Roll on 50ml', brand: 'RED DIAMOND', sku_category: 'DEODORANT', sku_size: 'REGULAR PACK', is_unilever: false, kd_unit: 250 }
    ],
    'SKIN CARE': [
      { id: 'sample-14', sku_name: 'Vaseline Jelly 100g', brand: 'VASELINE', sku_category: 'SKIN CARE', sku_size: 'REGULAR PACK', is_unilever: true, kd_unit: 500 },
      { id: 'sample-15', sku_name: 'Lux Soap 90g', brand: 'LUX', sku_category: 'SKIN CARE', sku_size: 'REGULAR PACK', is_unilever: true, kd_unit: 200 },
      { id: 'sample-16', sku_name: 'Cussons Baby Jelly 50g', brand: 'CUSSONS', sku_category: 'SKIN CARE', sku_size: 'SMALL PACK', is_unilever: false, kd_unit: 200 },
      { id: 'sample-17', sku_name: 'NBC Jelly 250g', brand: 'NBC', sku_category: 'SKIN CARE', sku_size: 'LARGE PACK', is_unilever: false, kd_unit: 500 }
    ],
    'SALVORY': [
      { id: 'sample-18', sku_name: 'Hellmanns Mayonnaise 250ml', brand: 'HELLMANNS', sku_category: 'SALVORY', sku_size: 'REGULAR PACK', is_unilever: true, kd_unit: 800 },
      { id: 'sample-19', sku_name: 'Sample Salvory Product 2', brand: 'BRAND D', sku_category: 'SALVORY', sku_size: 'SMALL PACK', is_unilever: false, kd_unit: 300 }
    ]
  };

  // Fetch products for the selected category and market
  useEffect(() => {
    const fetchProducts = async () => {
      setLoading(true);
      setError(null);

      try {
        // Try to fetch from API first
        const apiProducts = await api.getPrices();
        console.log('All API products:', apiProducts.length);

        // Filter by category and market type if market is selected
        let filteredProducts = apiProducts.filter(
          product => product.sku_category?.toUpperCase() === selectedCategory.toUpperCase()
        );

        // If market type is selected, filter by market type as well
        if (selectedMarketType) {
          filteredProducts = filteredProducts.filter(
            product => product.market_type === selectedMarketType
          );
        }

        console.log(`Filtered products for ${selectedCategory} ${selectedMarketType ? `in ${selectedMarketType}` : ''}:`, filteredProducts.length);

        // Always include sample data to ensure we have products to show
        const sampleProducts = SAMPLE_PRODUCTS[selectedCategory] || [];

        // Combine API products with sample products, removing duplicates
        const combinedProducts = [...filteredProducts];

        // Add sample products that don't exist in API data
        sampleProducts.forEach(sampleProduct => {
          const exists = filteredProducts.some(apiProduct =>
            apiProduct.sku_name === sampleProduct.sku_name &&
            apiProduct.brand === sampleProduct.brand
          );
          if (!exists) {
            combinedProducts.push({
              ...sampleProduct,
              market_type: selectedMarketType || 'OPEN_MARKET' // Set default market type
            });
          }
        });

        // Store all products and set up pagination
        setAllProducts(combinedProducts);
        setCurrentPage(0); // Reset to first page

        // Show first 10 products
        const paginatedProducts = combinedProducts.slice(0, PRODUCTS_PER_PAGE);
        setAvailableProducts(paginatedProducts);

        if (combinedProducts.length === 0) {
          setError(`No products found for ${selectedCategory}${selectedMarketType ? ` in ${selectedMarketType}` : ''}. You can create new SKUs using the "Create New SKU" option.`);
        }
      } catch (err) {
        console.error('Error fetching products:', err);
        // Fallback to sample data
        const sampleProducts = SAMPLE_PRODUCTS[selectedCategory] || [];
        const sampleWithMarket = sampleProducts.map(product => ({
          ...product,
          market_type: selectedMarketType || 'OPEN_MARKET'
        }));

        // Store all products and set up pagination
        setAllProducts(sampleWithMarket);
        setCurrentPage(0);

        // Show first 10 products
        const paginatedProducts = sampleWithMarket.slice(0, PRODUCTS_PER_PAGE);
        setAvailableProducts(paginatedProducts);
        setError('Using sample data. Could not connect to API.');
      } finally {
        setLoading(false);
      }
    };

    if (selectedCategory) {
      fetchProducts();
    }
  }, [selectedCategory, selectedMarketType]);

  // Handle price input change
  const handlePriceChange = (productId: string, price: string) => {
    setPriceEntries(prev => ({
      ...prev,
      [productId]: price
    }));
  };

  // Handle pagination
  const handleNextPage = () => {
    const nextPage = currentPage + 1;
    const startIndex = nextPage * PRODUCTS_PER_PAGE;
    const endIndex = startIndex + PRODUCTS_PER_PAGE;

    if (startIndex < allProducts.length) {
      setCurrentPage(nextPage);
      setAvailableProducts(allProducts.slice(startIndex, endIndex));
    }
  };

  const handlePrevPage = () => {
    const prevPage = currentPage - 1;
    const startIndex = prevPage * PRODUCTS_PER_PAGE;
    const endIndex = startIndex + PRODUCTS_PER_PAGE;

    if (prevPage >= 0) {
      setCurrentPage(prevPage);
      setAvailableProducts(allProducts.slice(startIndex, endIndex));
    }
  };

  const getTotalPages = () => Math.ceil(allProducts.length / PRODUCTS_PER_PAGE);
  const getCurrentRange = () => {
    const start = currentPage * PRODUCTS_PER_PAGE + 1;
    const end = Math.min((currentPage + 1) * PRODUCTS_PER_PAGE, allProducts.length);
    return { start, end };
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!selectedMarketType) {
      setError('Please select a market type');
      return;
    }

    // Get all products with prices entered
    const productsWithPrices = availableProducts.filter(
      product => priceEntries[product.id || ''] && parseFloat(priceEntries[product.id || '']) > 0
    );

    if (productsWithPrices.length === 0) {
      setError('Please enter at least one price');
      return;
    }

    setError(null);
    let successCount = 0;
    let errorCount = 0;

    // Submit each product with its price
    for (const product of productsWithPrices) {
      try {
        const price = parseFloat(priceEntries[product.id || '']);

        const formData: ProductFormData = {
          sku_name: product.sku_name,
          sku_category: product.sku_category || selectedCategory,
          sku_size: product.sku_size || 'REGULAR PACK',
          brand: product.brand || '',
          market_type: selectedMarketType,
          location: 'Lagos', // Default location
          kd_case: price * 6, // Assuming 6 units per case
          kd_unit: price,
          sku_description: product.sku_description || `${product.sku_name} - ${product.brand}`,
          wholesale_price: 0,
          open_market_price: 0,
          ng_price: 0,
          small_supermarket_price: 0,
          is_unilever: product.is_unilever || false
        };

        await onSubmit(formData);
        successCount++;
      } catch (err) {
        console.error(`Error submitting ${product.sku_name}:`, err);
        errorCount++;
      }
    }

    // Show summary message
    if (successCount > 0 && errorCount === 0) {
      setError(null);
      // Clear the price entries after successful submission
      setPriceEntries({});
    } else if (errorCount > 0) {
      setError(`${successCount} products updated successfully, ${errorCount} failed. Please try again for failed items.`);
    }
  };

  if (loading) {
    return (
      <div className="max-w-md mx-auto bg-white min-h-screen p-4">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading products...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-md mx-auto bg-white min-h-screen">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded-b-lg shadow-lg">
        <div className="flex items-center mb-2">
          <button 
            onClick={onBack}
            className="mr-3 text-white hover:bg-white/20 p-1 rounded"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <div>
            <h1 className={`${fontSize === 'text-sm' ? 'text-lg' : fontSize} font-bold`}>Price Entry</h1>
            <p className="text-blue-100 text-sm">Category: {selectedCategory}</p>
          </div>
        </div>
      </div>

      <div className="p-4">
        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        {/* Market Type Selection */}
        <div className="mb-6">
          <label className={`block ${fontSize === 'text-sm' ? 'text-base' : fontSize} font-semibold text-gray-800 mb-3`}>
            Select Market Type *
          </label>
          <select
            value={selectedMarketType}
            onChange={(e) => setSelectedMarketType(e.target.value)}
            className="w-full p-3 border-2 border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 bg-white"
          >
            <option value="">Choose Market Type</option>
            {MARKET_TYPES.map(market => (
              <option key={market.value} value={market.value}>
                {market.label}
              </option>
            ))}
          </select>
        </div>

        {/* Info Box */}
        {selectedMarketType && allProducts.length > PRODUCTS_PER_PAGE && (
          <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-start">
              <svg className="w-5 h-5 text-yellow-500 mt-0.5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div>
                <p className="text-yellow-800 text-xs font-medium">Multiple Products Available</p>
                <p className="text-yellow-700 text-xs mt-1">
                  Found {allProducts.length} products. Showing 10 at a time. You can update prices on this page and use pagination to see more products.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Product List with Price Inputs */}
        {selectedMarketType && (
          <div className="space-y-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className={`${fontSize === 'text-sm' ? 'text-base' : fontSize} font-semibold text-gray-800`}>
                Update Prices for {MARKET_TYPES.find(m => m.value === selectedMarketType)?.label}
              </h3>
              <div className="text-sm text-gray-500">
                {allProducts.length > 0 && (
                  <span>
                    Showing {getCurrentRange().start}-{getCurrentRange().end} of {allProducts.length} products
                  </span>
                )}
              </div>
            </div>

            {/* Pagination Controls - Top */}
            {allProducts.length > PRODUCTS_PER_PAGE && (
              <div className="flex items-center justify-between mb-4 p-3 bg-gray-50 rounded-lg">
                <button
                  onClick={handlePrevPage}
                  disabled={currentPage === 0}
                  className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                    currentPage === 0
                      ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                      : 'bg-blue-500 text-white hover:bg-blue-600'
                  }`}
                >
                  ← Previous
                </button>

                <span className="text-sm text-gray-600">
                  Page {currentPage + 1} of {getTotalPages()}
                </span>

                <button
                  onClick={handleNextPage}
                  disabled={currentPage >= getTotalPages() - 1}
                  className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                    currentPage >= getTotalPages() - 1
                      ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                      : 'bg-blue-500 text-white hover:bg-blue-600'
                  }`}
                >
                  Next →
                </button>
              </div>
            )}

            {availableProducts.length === 0 ? (
              <div className="text-center py-8">
                <div className="text-gray-400 mb-2">
                  <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-4m-12 0H4m8 0V9m0 4v6" />
                  </svg>
                </div>
                <p className="text-gray-600">No products found for this category and market combination.</p>
                <p className="text-sm text-gray-500 mt-1">Try selecting a different market type or create a new SKU.</p>
              </div>
            ) : (
              <>
                {availableProducts.map((product, index) => {
                  const productKey = product.id || `${product.sku_name}-${index}`;
                  const currentPrice = product.kd_unit || 0;
                  const isUnilever = product.is_unilever || false;

                  return (
                    <div key={productKey} className={`p-4 rounded-lg border transition-all duration-200 ${
                      priceEntries[productKey] ? 'bg-blue-50 border-blue-200' : 'bg-gray-50 border-gray-200'
                    }`}>
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className={`${fontSize === 'text-sm' ? 'text-sm' : fontSize} font-medium text-gray-900`}>
                              {product.sku_name}
                            </h4>
                            {isUnilever && (
                              <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full font-medium">
                                Unilever
                              </span>
                            )}
                          </div>
                          <p className={`${fontSize === 'text-sm' ? 'text-xs' : 'text-sm'} text-gray-600 mb-1`}>
                            {product.brand} • {product.sku_size}
                          </p>
                          {currentPrice > 0 && (
                            <p className="text-xs text-gray-500">
                              Current price: ₦{currentPrice.toLocaleString()}
                            </p>
                          )}
                        </div>
                        <div className="ml-4 w-28">
                          <label className="block text-xs text-gray-600 mb-1">New Price (₦)</label>
                          <input
                            type="number"
                            placeholder={currentPrice > 0 ? currentPrice.toString() : "Enter price"}
                            value={priceEntries[productKey] || ''}
                            onChange={(e) => handlePriceChange(productKey, e.target.value)}
                            className="w-full p-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 text-right text-sm"
                            min="0"
                            step="0.01"
                          />
                        </div>
                      </div>
                    </div>
                  );
                })}

                {/* Pagination Controls - Bottom */}
                {allProducts.length > PRODUCTS_PER_PAGE && (
                  <div className="flex items-center justify-between mt-6 p-3 bg-gray-50 rounded-lg">
                    <button
                      onClick={handlePrevPage}
                      disabled={currentPage === 0}
                      className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                        currentPage === 0
                          ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                          : 'bg-blue-500 text-white hover:bg-blue-600'
                      }`}
                    >
                      ← Previous
                    </button>

                    <span className="text-sm text-gray-600">
                      Page {currentPage + 1} of {getTotalPages()}
                    </span>

                    <button
                      onClick={handleNextPage}
                      disabled={currentPage >= getTotalPages() - 1}
                      className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                        currentPage >= getTotalPages() - 1
                          ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                          : 'bg-blue-500 text-white hover:bg-blue-600'
                      }`}
                    >
                      Next →
                    </button>
                  </div>
                )}

                {/* Submit Button */}
                <div className="mt-6">
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                    <p className="text-blue-800 text-sm font-medium">Ready to submit?</p>
                    <p className="text-blue-700 text-xs mt-1">
                      {Object.keys(priceEntries).filter(key => priceEntries[key] && parseFloat(priceEntries[key]) > 0).length} products have new prices entered.
                      {allProducts.length > PRODUCTS_PER_PAGE && (
                        <span className="block mt-1">
                          Note: You can navigate through pages to update more products.
                        </span>
                      )}
                    </p>
                  </div>

                  <button
                    onClick={handleSubmit}
                    disabled={Object.keys(priceEntries).filter(key => priceEntries[key] && parseFloat(priceEntries[key]) > 0).length === 0}
                    className={`w-full py-3 px-6 rounded-xl font-semibold shadow-lg transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98] ${
                      Object.keys(priceEntries).filter(key => priceEntries[key] && parseFloat(priceEntries[key]) > 0).length === 0
                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                        : 'bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:shadow-xl'
                    }`}
                  >
                    Update Prices ({Object.keys(priceEntries).filter(key => priceEntries[key] && parseFloat(priceEntries[key]) > 0).length})
                    <svg className="w-5 h-5 ml-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </button>
                </div>
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default SKUPriceForm;
