import React, { useState, useEffect } from 'react';
import { api } from '../services/api';
import { ProductFormData, Product } from './ProductSelectionForm';

interface SKUPriceFormProps {
  selectedCategory: string;
  onBack: () => void;
  onSubmit: (data: ProductFormData) => void;
  fontSize?: string;
}

// Market type options
const MARKET_TYPES = [
  { value: 'OPEN_MARKET', label: 'Open Market' },
  { value: 'NG', label: 'NG' },
  { value: 'SMALL_SUPERMARKET', label: 'Small Supermarket' },
  { value: 'WHOLESALE', label: 'Wholesale' }
];

const SKUPriceForm: React.FC<SKUPriceFormProps> = ({
  selectedCategory,
  onBack,
  onSubmit,
  fontSize = 'text-sm'
}) => {
  const [availableProducts, setAvailableProducts] = useState<Product[]>([]);
  const [selectedMarketType, setSelectedMarketType] = useState<string>('');
  const [priceEntries, setPriceEntries] = useState<{ [key: string]: string }>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Sample products for each category
  const SAMPLE_PRODUCTS: { [key: string]: Product[] } = {
    'NUTRITION': [
      { id: 'sample-1', sku_name: 'Maggi Cube 4g', brand: 'MAGGI', sku_category: 'NUTRITION', sku_size: 'SMALL PACK' },
      { id: 'sample-2', sku_name: 'Onga Cube 4g', brand: 'ONGA', sku_category: 'NUTRITION', sku_size: 'SMALL PACK' },
      { id: 'sample-3', sku_name: 'Knorr Cube 4g', brand: 'KNORR', sku_category: 'NUTRITION', sku_size: 'SMALL PACK' }
    ],
    'ORAL CARE': [
      { id: 'sample-4', sku_name: 'Oral B Toothpaste 100g', brand: 'ORAL B', sku_category: 'ORAL CARE', sku_size: 'REGULAR PACK' },
      { id: 'sample-5', sku_name: 'Colgate Toothpaste 50g', brand: 'COLGATE', sku_category: 'ORAL CARE', sku_size: 'SMALL PACK' },
      { id: 'sample-6', sku_name: 'Close Up Toothpaste 75g', brand: 'CLOSE UP', sku_category: 'ORAL CARE', sku_size: 'REGULAR PACK' }
    ],
    'DEODORANT': [
      { id: 'sample-7', sku_name: 'Rexona Spray 150ml', brand: 'REXONA', sku_category: 'DEODORANT', sku_size: 'REGULAR PACK' },
      { id: 'sample-8', sku_name: 'Sure Spray 100ml', brand: 'SURE', sku_category: 'DEODORANT', sku_size: 'SMALL PACK' },
      { id: 'sample-9', sku_name: 'Nivea Roll on 50ml', brand: 'NIVEA', sku_category: 'DEODORANT', sku_size: 'REGULAR PACK' },
      { id: 'sample-10', sku_name: 'Enchanter Roll on 50ml', brand: 'ENCHANTER', sku_category: 'DEODORANT', sku_size: 'REGULAR PACK' },
      { id: 'sample-11', sku_name: 'Red Diamond Roll on 50ml', brand: 'RED DIAMOND', sku_category: 'DEODORANT', sku_size: 'REGULAR PACK' }
    ],
    'SKIN CARE': [
      { id: 'sample-12', sku_name: 'Cussons Baby Jelly 50g', brand: 'CUSSONS', sku_category: 'SKIN CARE', sku_size: 'SMALL PACK' },
      { id: 'sample-13', sku_name: 'NBC Jelly 250g', brand: 'NBC', sku_category: 'SKIN CARE', sku_size: 'REGULAR PACK' },
      { id: 'sample-14', sku_name: 'Fressia Jelly 100g', brand: 'FRESSIA', sku_category: 'SKIN CARE', sku_size: 'REGULAR PACK' }
    ],
    'SALVORY': [
      { id: 'sample-15', sku_name: 'Sample Salvory Product 1', brand: 'BRAND C', sku_category: 'SALVORY', sku_size: 'REGULAR PACK' },
      { id: 'sample-16', sku_name: 'Sample Salvory Product 2', brand: 'BRAND D', sku_category: 'SALVORY', sku_size: 'SMALL PACK' }
    ]
  };

  // Fetch products for the selected category
  useEffect(() => {
    const fetchProducts = async () => {
      setLoading(true);
      setError(null);
      
      try {
        // Try to fetch from API first
        const apiProducts = await api.getPrices();
        const categoryProducts = apiProducts.filter(
          product => product.sku_category?.toUpperCase() === selectedCategory.toUpperCase()
        );

        // If we have API products, use them; otherwise use sample data
        if (categoryProducts.length > 0) {
          setAvailableProducts(categoryProducts);
        } else {
          // Use sample data for the category
          const sampleProducts = SAMPLE_PRODUCTS[selectedCategory] || [];
          setAvailableProducts(sampleProducts);
        }
      } catch (err) {
        console.error('Error fetching products:', err);
        // Fallback to sample data
        const sampleProducts = SAMPLE_PRODUCTS[selectedCategory] || [];
        setAvailableProducts(sampleProducts);
        setError('Using sample data. Could not connect to API.');
      } finally {
        setLoading(false);
      }
    };

    if (selectedCategory) {
      fetchProducts();
    }
  }, [selectedCategory]);

  // Handle price input change
  const handlePriceChange = (productId: string, price: string) => {
    setPriceEntries(prev => ({
      ...prev,
      [productId]: price
    }));
  };

  // Handle form submission
  const handleSubmit = () => {
    if (!selectedMarketType) {
      setError('Please select a market type');
      return;
    }

    // Get all products with prices entered
    const productsWithPrices = availableProducts.filter(
      product => priceEntries[product.id || ''] && parseFloat(priceEntries[product.id || '']) > 0
    );

    if (productsWithPrices.length === 0) {
      setError('Please enter at least one price');
      return;
    }

    // Submit each product with its price
    productsWithPrices.forEach(product => {
      const price = parseFloat(priceEntries[product.id || '']);
      
      const formData: ProductFormData = {
        sku_name: product.sku_name,
        sku_category: product.sku_category || selectedCategory,
        sku_size: product.sku_size || 'REGULAR PACK',
        brand: product.brand || '',
        market_type: selectedMarketType,
        location: 'Lagos', // Default location
        kd_case: price * 6, // Assuming 6 units per case
        kd_unit: price,
        sku_description: `${product.sku_name} - ${product.brand}`,
        wholesale_price: 0,
        open_market_price: 0,
        ng_price: 0,
        small_supermarket_price: 0,
        is_unilever: false
      };

      onSubmit(formData);
    });
  };

  if (loading) {
    return (
      <div className="max-w-md mx-auto bg-white min-h-screen p-4">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading products...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-md mx-auto bg-white min-h-screen">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded-b-lg shadow-lg">
        <div className="flex items-center mb-2">
          <button 
            onClick={onBack}
            className="mr-3 text-white hover:bg-white/20 p-1 rounded"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <div>
            <h1 className={`${fontSize === 'text-sm' ? 'text-lg' : fontSize} font-bold`}>Price Entry</h1>
            <p className="text-blue-100 text-sm">Category: {selectedCategory}</p>
          </div>
        </div>
      </div>

      <div className="p-4">
        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        {/* Market Type Selection */}
        <div className="mb-6">
          <label className={`block ${fontSize === 'text-sm' ? 'text-base' : fontSize} font-semibold text-gray-800 mb-3`}>
            Select Market Type *
          </label>
          <select
            value={selectedMarketType}
            onChange={(e) => setSelectedMarketType(e.target.value)}
            className="w-full p-3 border-2 border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 bg-white"
          >
            <option value="">Choose Market Type</option>
            {MARKET_TYPES.map(market => (
              <option key={market.value} value={market.value}>
                {market.label}
              </option>
            ))}
          </select>
        </div>

        {/* Product List with Price Inputs */}
        {selectedMarketType && (
          <div className="space-y-4">
            <h3 className={`${fontSize === 'text-sm' ? 'text-base' : fontSize} font-semibold text-gray-800 mb-4`}>
              Enter Prices for {MARKET_TYPES.find(m => m.value === selectedMarketType)?.label}
            </h3>
            
            {availableProducts.map((product, index) => (
              <div key={product.id || index} className="bg-gray-50 p-4 rounded-lg border">
                <div className="flex justify-between items-center">
                  <div className="flex-1">
                    <h4 className={`${fontSize === 'text-sm' ? 'text-sm' : fontSize} font-medium text-gray-900`}>
                      {product.sku_name}
                    </h4>
                    <p className={`${fontSize === 'text-sm' ? 'text-xs' : 'text-sm'} text-gray-600`}>
                      {product.brand} • {product.sku_size}
                    </p>
                  </div>
                  <div className="ml-4 w-24">
                    <input
                      type="number"
                      placeholder="Price"
                      value={priceEntries[product.id || ''] || ''}
                      onChange={(e) => handlePriceChange(product.id || '', e.target.value)}
                      className="w-full p-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 text-right"
                      min="0"
                      step="0.01"
                    />
                  </div>
                </div>
              </div>
            ))}

            {/* Submit Button */}
            <div className="mt-6">
              <button
                onClick={handleSubmit}
                className="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-6 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]"
              >
                Submit Prices
                <svg className="w-5 h-5 ml-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SKUPriceForm;
