// Simple build script for Vercel
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Run the build command
console.log('Running build command...');
try {
  execSync('npm run build', { stdio: 'inherit' });
  console.log('Build completed successfully');
} catch (error) {
  console.error('Build failed:', error);
  process.exit(1);
}

// Create database directory
console.log('Creating database directory...');
const databaseDir = path.join(__dirname, 'dist', 'database');
if (!fs.existsSync(databaseDir)) {
  try {
    fs.mkdirSync(databaseDir, { recursive: true });
    console.log('Database directory created successfully');
  } catch (err) {
    console.error('Error creating database directory:', err);
  }
}

// Create a simple index.html file in the database directory to prevent 404s
try {
  const indexContent = `
<!DOCTYPE html>
<html>
<head>
  <title>Database Directory</title>
</head>
<body>
  <h1>Database Directory</h1>
  <p>This directory contains database files for the application.</p>
</body>
</html>
`;
  fs.writeFileSync(path.join(databaseDir, 'index.html'), indexContent);
  console.log('Created index.html in database directory');
} catch (error) {
  console.error('Error creating index.html:', error);
}

console.log('Build process completed');
