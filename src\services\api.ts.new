import axios from 'axios';
import { getApiUrl, isProduction, getEnvironmentName, getForceMockData, getDirectApiUrl } from '../utils/environment';

// Load mock products from localStorage if available - only used as fallback
const loadMockProductsFromStorage = (): any[] => {
  try {
    const savedProducts = localStorage.getItem('apiMockProducts');
    if (savedProducts) {
      const parsedProducts = JSON.parse(savedProducts);
      console.log('Loaded', parsedProducts.length, 'mock products from localStorage');
      return parsedProducts;
    }
  } catch (error) {
    console.error('Error loading mock products from localStorage:', error);
  }
  return [];
};

// Save mock products to localStorage - only used as fallback
const saveMockProductsToStorage = (products: any[]): void => {
  try {
    localStorage.setItem('apiMockProducts', JSON.stringify(products));
    console.log('Saved', products.length, 'mock products to localStorage');
  } catch (error) {
    console.error('Error saving mock products to localStorage:', error);
  }
};

// Check if we should force using mock data from environment variable
const forceMockData = getForceMockData();

// Configuration for API access
const API_CONFIG = {
  // Primary API URL - use environment variable or fallback to default
  baseUrl: getApiUrl() || 'http://localhost:8000/api',

  // Direct API URL without CORS proxy
  directUrl: getDirectApiUrl(),

  // Flag to indicate if we should use the direct URL first
  useDirectUrlFirst: true,

  // Flag to indicate if direct URL is working
  directUrlWorking: false,

  // Flag to indicate if we should use mock data (set to true if API is unreachable)
  useMockData: forceMockData || false,

  // Flag to indicate if we've already checked the API connection
  connectionChecked: forceMockData, // If forcing mock data, consider connection already checked

  // Timeout for API requests in milliseconds
  timeout: 5000,

  // Longer timeout for file uploads
  uploadTimeout: 30000,

  // In-memory storage for mock data when API is unavailable
  mockProducts: loadMockProductsFromStorage()
};

// Check if we're using a CORS proxy
const isUsingCorsProxy = API_CONFIG.baseUrl.includes('corsproxy.io');

// Create an axios instance with default config (using CORS proxy if needed)
const apiClient = axios.create({
  baseURL: API_CONFIG.baseUrl,
  timeout: API_CONFIG.timeout,
  headers: {
    'Content-Type': 'application/json',
  }
});

// Create a direct API client (without CORS proxy)
const directApiClient = API_CONFIG.directUrl ? axios.create({
  baseURL: API_CONFIG.directUrl,
  timeout: API_CONFIG.timeout,
  headers: {
    'Content-Type': 'application/json',
  }
}) : null;

// Add response interceptor to handle CORS proxy responses
apiClient.interceptors.response.use(
  (response) => {
    // If we're using a CORS proxy, the response might be wrapped
    if (isUsingCorsProxy && response.data && typeof response.data === 'string') {
      try {
        // Try to parse the response as JSON
        const parsedData = JSON.parse(response.data);
        response.data = parsedData;
      } catch (error) {
        // If parsing fails, keep the original response
        console.warn('Failed to parse CORS proxy response as JSON:', error);
      }
    }
    return response;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Function to check if the API is reachable
const checkApiConnection = async (): Promise<boolean> => {
  // If we're forcing mock data via environment variable, don't even try to connect
  if (forceMockData) {
    console.log('Forcing mock data based on environment configuration');
    API_CONFIG.useMockData = true;
    API_CONFIG.connectionChecked = true;
    return false;
  }

  // If we've already checked the connection, return the cached result
  if (API_CONFIG.connectionChecked) {
    return !API_CONFIG.useMockData;
  }

  // Try direct URL first if available and enabled
  if (API_CONFIG.directUrl && API_CONFIG.useDirectUrlFirst && directApiClient) {
    try {
      console.log(`Testing direct API connection in ${getEnvironmentName()} environment...`);
      console.log(`Using direct API URL: ${API_CONFIG.directUrl}`);

      // Try to connect to the health-check endpoint
      const healthCheckUrl = `${API_CONFIG.directUrl}/health-check`;
      console.log('Trying to connect to direct health-check endpoint:', healthCheckUrl);

      const response = await axios.get(healthCheckUrl, {
        timeout: API_CONFIG.timeout
      });

      // Check if the response is valid
      if (response.status === 200) {
        console.log('Direct API connection successful');
        API_CONFIG.useMockData = false;
        API_CONFIG.connectionChecked = true;
        API_CONFIG.directUrlWorking = true;
        return true;
      }
    } catch (directError: any) {
      console.warn('Direct API connection failed, will try CORS proxy');
      console.warn(`Failed connecting to: ${API_CONFIG.directUrl}`);

      if (directError.response) {
        console.warn(`Status: ${directError.response.status}, Data:`, directError.response.data);
      } else if (directError.request) {
        console.warn('No response received from direct server');
      } else {
        console.warn(`Error message: ${directError.message}`);
      }

      // Try alternative endpoint with direct URL
      try {
        console.log('Trying alternative direct endpoint: /competitor-prices/');
        const altDirectUrl = `${API_CONFIG.directUrl}/competitor-prices/`;

        const altDirectResponse = await axios.get(altDirectUrl, {
          timeout: API_CONFIG.timeout
        });

        if (altDirectResponse.status === 200) {
          console.log('Alternative direct API endpoint connection successful');
          API_CONFIG.useMockData = false;
          API_CONFIG.connectionChecked = true;
          API_CONFIG.directUrlWorking = true;
          return true;
        }
      } catch (altDirectError) {
        console.warn('Alternative direct endpoint also failed');
        // Continue to try the CORS proxy
      }
    }
  }

  // If direct URL failed or is not available, try the CORS proxy
  try {
    console.log(`Testing CORS proxy API connection in ${getEnvironmentName()} environment...`);
    console.log(`Using API URL: ${API_CONFIG.baseUrl}`);

    // Try to connect to the health-check endpoint
    // If using CORS proxy, we need to handle the URL differently
    const healthCheckUrl = isUsingCorsProxy
      ? `${API_CONFIG.baseUrl.split('?')[0]}?${encodeURIComponent(`${API_CONFIG.baseUrl.split('?')[1]}/health-check`)}`
      : `${API_CONFIG.baseUrl}/health-check`;

    console.log('Trying to connect to health-check endpoint:', healthCheckUrl);

    const response = await axios.get(healthCheckUrl, {
      timeout: API_CONFIG.timeout
    });

    // Check if the response is valid
    if (response.status === 200) {
      console.log('API connection successful via CORS proxy');
      API_CONFIG.useMockData = false;
      API_CONFIG.connectionChecked = true;
      return true;
    } else {
      console.warn(`API returned unexpected status: ${response.status}`);
      API_CONFIG.useMockData = true;
      API_CONFIG.connectionChecked = true;
      return false;
    }
  } catch (error: any) {
    // Log detailed error information
    console.error(`API connection failed in ${getEnvironmentName()} environment, will use mock data`);
    console.error(`Failed connecting to: ${API_CONFIG.baseUrl}`);

    if (error.response) {
      console.error(`Status: ${error.response.status}, Data:`, error.response.data);
    } else if (error.request) {
      console.error('No response received from server');
    } else {
      console.error(`Error message: ${error.message}`);
    }

    // Try an alternative endpoint if health-check fails
    try {
      console.log('Trying alternative endpoint: /competitor-prices/');

      // If using CORS proxy, we need to handle the URL differently
      const altEndpointUrl = isUsingCorsProxy
        ? `${API_CONFIG.baseUrl.split('?')[0]}?${encodeURIComponent(`${API_CONFIG.baseUrl.split('?')[1]}/competitor-prices/`)}`
        : `${API_CONFIG.baseUrl}/competitor-prices/`;

      console.log('Alternative endpoint URL:', altEndpointUrl);

      const altResponse = await axios.get(altEndpointUrl, {
        timeout: API_CONFIG.timeout
      });

      if (altResponse.status === 200) {
        console.log('Alternative API endpoint connection successful');
        API_CONFIG.useMockData = false;
        API_CONFIG.connectionChecked = true;
        return true;
      }
    } catch (altError) {
      console.error('Alternative endpoint also failed');
    }

    API_CONFIG.useMockData = true;
    API_CONFIG.connectionChecked = true;
    return false;
  }
};

// Helper function to get the appropriate API client
const getApiClient = () => {
  // If direct URL is working and available, use it
  if (API_CONFIG.directUrlWorking && directApiClient) {
    console.log('Using direct API client');
    return directApiClient;
  }

  // Otherwise use the regular client (which might use CORS proxy)
  console.log('Using regular API client (possibly with CORS proxy)');
  return apiClient;
};

interface PriceSubmission {
  sku_name: string;
  sku_category: string;
  sku_size_category: string;
  brand: string;
  market_type: string;
  location: string;
  selling_price_case: number;
  selling_price_unit: number;
  sku_description?: string; // Optional field
}

const api = {
  testConnection: async () => {
    try {
      const isApiReachable = await checkApiConnection();

      if (isApiReachable) {
        return { success: true, data: { message: 'API is reachable' } };
      } else {
        console.log('API is not reachable, using mock data');
        return { success: true, data: { message: 'Using mock data' } };
      }
    } catch (error: any) {
      console.error('API connection test failed:', error.message);

      // Set to use mock data
      API_CONFIG.useMockData = true;
      API_CONFIG.connectionChecked = true;

      return {
        success: true,
        data: {
          message: 'Using mock data due to connection error',
          error: error.message
        }
      };
    }
  },

  getCategoryChoices: async () => {
    try {
      // Check if we should use mock data
      await checkApiConnection();

      if (API_CONFIG.useMockData) {
        console.log('Using mock data for category choices');

        // Return mock category choices
        return {
          sku_categories: {
            'DEODORANT': 'DEODORANT',
            'SKIN CARE': 'SKIN CARE',
            'ORAL CARE': 'ORAL CARE',
            'NUTRITION': 'NUTRITION'
          },
          sku_size_categories: {
            'BULK PACK': 'BULK PACK',
            'MID PACK': 'MID PACK',
            'REGULAR PACK': 'REGULAR PACK',
            'SMALL PACK': 'SMALL PACK'
          },
          locations: ['Lagos', 'Abuja', 'Port Harcourt', 'Kano', 'Ibadan']
        };
      }

      // Using the /competitor-prices/get_category_choices/ endpoint from the documentation
      console.log('Fetching category choices from API');
      const client = getApiClient();
      const response = await client.get('/competitor-prices/get_category_choices/');
      return response.data;
    } catch (error) {
      console.error('Error fetching category choices:', error);

      // Return mock data as fallback
      return {
        sku_categories: {
          'DEODORANT': 'DEODORANT',
          'SKIN CARE': 'SKIN CARE',
          'ORAL CARE': 'ORAL CARE',
          'NUTRITION': 'NUTRITION'
        },
        sku_size_categories: {
          'BULK PACK': 'BULK PACK',
          'MID PACK': 'MID PACK',
          'REGULAR PACK': 'REGULAR PACK',
          'SMALL PACK': 'SMALL PACK'
        },
        locations: ['Lagos', 'Abuja', 'Port Harcourt', 'Kano', 'Ibadan']
      };
    }
  },

  submitPrice: async (data: PriceSubmission) => {
    try {
      console.log('Submitting price data to API:', data);

      // Check if we should use mock data
      await checkApiConnection();

      if (API_CONFIG.useMockData) {
        // If using mock data, create a mock response
        console.log('Using mock implementation for price submission');

        // Generate a random ID for the mock response
        const mockId = Math.random().toString(36).substring(2, 15);

        // Create a mock response
        const mockResponse = {
          id: mockId,
          ...data,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        // Add the new product to our in-memory mock storage
        API_CONFIG.mockProducts.push(mockResponse);
        console.log('Added new product to mock storage. Total products:', API_CONFIG.mockProducts.length);

        // Save to localStorage for persistence
        saveMockProductsToStorage(API_CONFIG.mockProducts);

        console.log('Mock response:', mockResponse);

        // Simulate a delay to mimic network latency
        await new Promise(resolve => setTimeout(resolve, 500));

        return mockResponse;
      }

      // If not using mock data, make the real API call
      console.log('Submitting price data to API endpoint');

      // Using the POST /competitor-prices/ endpoint from the documentation
      const client = getApiClient();
      const response = await client.post('/competitor-prices/', data);

      console.log('API response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error submitting price:', error);

      if (error.response) {
        console.error('Error response data:', error.response.data);
        console.error('Error response status:', error.response.status);
        console.error('Error response headers:', error.response.headers);
      } else if (error.request) {
        console.error('Error request:', error.request);
      } else {
        console.error('Error message:', error.message);
      }

      // Check if we should fall back to mock data
      if (API_CONFIG.useMockData) {
        console.log('Using mock fallback for price submission due to error');

        // Generate a random ID for the mock response
        const mockId = Math.random().toString(36).substring(2, 15);

        // Create a mock response
        const mockResponse = {
          id: mockId,
          ...data,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        // Add to our in-memory mock storage even in error case
        API_CONFIG.mockProducts.push(mockResponse);
        console.log('Added new product to mock storage after error. Total products:', API_CONFIG.mockProducts.length);

        // Save to localStorage for persistence
        saveMockProductsToStorage(API_CONFIG.mockProducts);

        return mockResponse;
      }

      // If we're not using mock data, rethrow the error to be handled by the caller
      throw error;
    }
  },

  getPrices: async (filters = {}) => {
    try {
      // Check if we should use mock data
      await checkApiConnection();

      if (API_CONFIG.useMockData) {
        // If using mock data, return mock prices
        console.log('Using mock implementation for getPrices');

        // Create default mock data if we don't have any products yet
        if (API_CONFIG.mockProducts.length === 0) {
          // Add some initial mock data
          const initialMockData = [
            {
              id: 'mock-1',
              sku_name: 'Nivea Roll on 50ml',
              sku_category: 'DEODORANT',
              sku_size_category: 'REGULAR PACK',
              brand: 'NIVEA',
              market_type: 'RETAIL',
              location: 'Lagos',
              selling_price_case: 2400,
              selling_price_unit: 400,
              created_at: new Date().toISOString()
            },
            {
              id: 'mock-2',
              sku_name: 'Enchanter Roll on 50ml',
              sku_category: 'DEODORANT',
              sku_size_category: 'REGULAR PACK',
              brand: 'ENCHANTER',
              market_type: 'RETAIL',
              location: 'Lagos',
              selling_price_case: 1800,
              selling_price_unit: 300,
              created_at: new Date().toISOString()
            },
            {
              id: 'mock-3',
              sku_name: 'Cussons Baby Jelly 50g',
              sku_category: 'SKIN CARE',
              sku_size_category: 'SMALL PACK',
              brand: 'CUSSONS',
              market_type: 'RETAIL',
              location: 'Lagos',
              selling_price_case: 1200,
              selling_price_unit: 200,
              created_at: new Date().toISOString()
            }
          ];

          // Add these to our mock products storage
          API_CONFIG.mockProducts = [...initialMockData];

          // Save to localStorage for persistence
          saveMockProductsToStorage(API_CONFIG.mockProducts);
        }

        console.log('Returning mock products:', API_CONFIG.mockProducts.length);

        // Simulate a delay to mimic network latency
        await new Promise(resolve => setTimeout(resolve, 500));

        return API_CONFIG.mockProducts;
      }

      // If not using mock data, make the real API call
      console.log('Fetching prices from API with filters:', filters);

      // Convert filters object to query parameters
      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, String(value));
        }
      });

      const queryString = params.toString() ? `?${params.toString()}` : '';
      console.log('Query string:', queryString);

      const client = getApiClient();
      const response = await client.get(`/competitor-prices/${queryString}`);
      console.log('API response for prices:', response.data);

      // The API might return paginated results with a 'results' property
      const products = response.data.results || response.data;
      return products;
    } catch (error) {
      console.error('Error fetching prices:', error);

      // Return mock data instead of empty array to ensure the app has data to work with
      console.log('Returning mock data due to error');

      // If we already have mock products, return those
      if (API_CONFIG.mockProducts.length > 0) {
        console.log('Using existing mock products:', API_CONFIG.mockProducts.length);
        return API_CONFIG.mockProducts;
      }

      // Otherwise, create some fallback data
      const fallbackData = [
        {
          id: 'error-fallback-1',
          sku_name: 'Nivea Roll on 50ml',
          sku_category: 'DEODORANT',
          sku_size_category: 'REGULAR PACK',
          brand: 'NIVEA',
          market_type: 'RETAIL',
          location: 'Lagos',
          selling_price_case: 2400,
          selling_price_unit: 400,
          created_at: new Date().toISOString()
        },
        {
          id: 'error-fallback-2',
          sku_name: 'Enchanter Roll on 50ml',
          sku_category: 'DEODORANT',
          sku_size_category: 'REGULAR PACK',
          brand: 'ENCHANTER',
          market_type: 'RETAIL',
          location: 'Lagos',
          selling_price_case: 1800,
          selling_price_unit: 300,
          created_at: new Date().toISOString()
        }
      ];

      // Store these for future use
      API_CONFIG.mockProducts = [...fallbackData];

      // Save to localStorage for persistence
      saveMockProductsToStorage(API_CONFIG.mockProducts);

      return fallbackData;
    }
  },

  getPriceById: async (id: string) => {
    try {
      // Check if we should use mock data
      await checkApiConnection();

      if (API_CONFIG.useMockData) {
        // If using mock data, return a mock price
        console.log(`Using mock implementation for getPriceById with ID ${id}`);

        // Try to find the product in our mock storage first
        const existingProduct = API_CONFIG.mockProducts.find(product => product.id === id);

        if (existingProduct) {
          console.log('Found existing product in mock storage:', existingProduct);
          return existingProduct;
        }

        // If not found, create a mock product
        console.log('Product not found in mock storage, creating a mock response');
        const mockResponse = {
          id,
          sku_name: `Mock Product ${id}`,
          sku_category: 'DEODORANT',
          sku_size_category: 'REGULAR PACK',
          brand: 'MOCK BRAND',
          market_type: 'RETAIL',
          location: 'Lagos',
          selling_price_case: 2000,
          selling_price_unit: 300,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        // Simulate a delay to mimic network latency
        await new Promise(resolve => setTimeout(resolve, 500));

        return mockResponse;
      }

      // If not using mock data, make the real API call
      console.log(`Fetching price with ID ${id} from API`);
      const client = getApiClient();
      const response = await client.get(`/competitor-prices/${id}/`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching price with ID ${id}:`, error);

      if (API_CONFIG.useMockData) {
        // Return a mock response instead of throwing
        return {
          id,
          sku_name: `Error Fallback Product ${id}`,
          sku_category: 'DEODORANT',
          sku_size_category: 'REGULAR PACK',
          brand: 'ERROR FALLBACK',
          market_type: 'RETAIL',
          location: 'Lagos',
          selling_price_case: 2000,
          selling_price_unit: 300,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
      }

      // If we're not using mock data, rethrow the error to be handled by the caller
      throw error;
    }
  },

  updatePrice: async (id: string, data: Partial<PriceSubmission>) => {
    try {
      // Log the data being sent to the API
      console.log(`Updating price with ID ${id}:`, data);

      // Check if we should use mock data
      await checkApiConnection();

      if (API_CONFIG.useMockData) {
        // If using mock data, create a mock response
        console.log('Using mock implementation for update');

        // Find and update the product in our mock storage
        const productIndex = API_CONFIG.mockProducts.findIndex(product => product.id === id);
        if (productIndex !== -1) {
          // Update the existing product
          API_CONFIG.mockProducts[productIndex] = {
            ...API_CONFIG.mockProducts[productIndex],
            ...data,
            updated_at: new Date().toISOString()
          };

          // Save to localStorage
          saveMockProductsToStorage(API_CONFIG.mockProducts);

          console.log('Updated product in mock storage. Total products:', API_CONFIG.mockProducts.length);
        }

        // Create a mock response
        const mockResponse = {
          id,
          ...data,
          updated_at: new Date().toISOString()
        };

        console.log('Mock response for update:', mockResponse);

        // Simulate a delay to mimic network latency
        await new Promise(resolve => setTimeout(resolve, 500));

        return mockResponse;
      }

      // If not using mock data, make the real API call
      console.log(`Updating price with ID ${id} via API`);

      // Using the PUT /competitor-prices/{id}/ endpoint from the documentation
      const client = getApiClient();
      const response = await client.put(`/competitor-prices/${id}/`, data);

      console.log('API response for price update:', response.data);
      return response.data;
    } catch (error: any) {
      console.error(`Error updating price with ID ${id}:`, error);

      // Log more detailed error information
      if (error.response) {
        console.error('Error response status:', error.response.status);
        console.error('Error response data:', error.response.data);

        // If there's validation error details, log them
        if (error.response.data && error.response.data.detail) {
          console.error('Validation errors:', error.response.data.detail);
        }
      } else if (error.request) {
        console.error('Error request:', error.request);
      } else {
        console.error('Error message:', error.message);
      }

      if (API_CONFIG.useMockData) {
        // Find and update the product in our mock storage
        const productIndex = API_CONFIG.mockProducts.findIndex(product => product.id === id);
        if (productIndex !== -1) {
          // Update the existing product
          API_CONFIG.mockProducts[productIndex] = {
            ...API_CONFIG.mockProducts[productIndex],
            ...data,
            updated_at: new Date().toISOString()
          };

          // Save to localStorage
          saveMockProductsToStorage(API_CONFIG.mockProducts);

          console.log('Updated product in mock storage after error. Total products:', API_CONFIG.mockProducts.length);
        }

        // Return a mock response instead of throwing
        return {
          id,
          ...data,
          updated_at: new Date().toISOString()
        };
      }

      // If we're not using mock data, rethrow the error to be handled by the caller
      throw error;
    }
  },

  uploadCSV: async (file: File) => {
    try {
      // Check if we should use mock data
      await checkApiConnection();

      if (API_CONFIG.useMockData) {
        // If using mock data, create a mock response
        console.log('Using mock implementation for CSV upload');

        // Simulate a delay to mimic network latency
        await new Promise(resolve => setTimeout(resolve, 1000));

        return {
          success: true,
          message: `File ${file.name} uploaded successfully (MOCK DATA - NOT SAVED TO DATABASE)`,
          count: Math.floor(Math.random() * 10) + 5 // Random number of records between 5-15
        };
      }

      // If not using mock data, make the real API call
      console.log('Uploading CSV file to API:', file.name);
      console.log('API URL:', `${API_CONFIG.baseUrl}/competitor-prices/upload/`);

      // Create a FormData object to send the file
      const formData = new FormData();
      formData.append('file', file);

      // Log the file being uploaded
      console.log('File being uploaded:', file);
      console.log('File size:', file.size, 'bytes');
      console.log('File type:', file.type);

      // Prepare the upload endpoint URL
      let uploadEndpoint = '/competitor-prices/upload/';

      // If using CORS proxy, we need to handle the URL differently for file uploads
      if (isUsingCorsProxy) {
        // For file uploads with CORS proxy, we might need a different approach
        // Some CORS proxies don't handle multipart/form-data well
        console.log('Warning: File uploads through CORS proxies may not work correctly');

        // Try to use the proxy with the full URL
        const baseUrlParts = API_CONFIG.baseUrl.split('?');
        if (baseUrlParts.length > 1) {
          const proxyUrl = baseUrlParts[0];
          const targetUrl = baseUrlParts[1];
          uploadEndpoint = `${proxyUrl}?${encodeURIComponent(`${targetUrl}/competitor-prices/upload/`)}`;

          // For this case, we'll use axios directly instead of apiClient
          console.log('Using direct axios call with full URL:', uploadEndpoint);

          const response = await axios.post(uploadEndpoint, formData, {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
            // Increase timeout for large files
            timeout: API_CONFIG.uploadTimeout
          });

          return response.data;
        }
      }

      // If not using CORS proxy or if we couldn't parse the URL correctly
      console.log('Using API client for upload to endpoint:', uploadEndpoint);

      // Try to use direct API client first for file uploads if available
      if (API_CONFIG.directUrlWorking && directApiClient) {
        console.log('Using direct API client for file upload');
        const directUploadEndpoint = '/competitor-prices/upload/';
        const response = await directApiClient.post(directUploadEndpoint, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          // Increase timeout for large files
          timeout: API_CONFIG.uploadTimeout
        });
        return response.data;
      }

      // Fall back to regular client if direct client is not available
      const client = getApiClient();
      const response = await client.post(uploadEndpoint, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        // Increase timeout for large files
        timeout: API_CONFIG.uploadTimeout
      });

      console.log('API response for CSV upload:', response.data);

      // If the upload was successful, also update our local cache
      if (response.data && response.data.success) {
        console.log('Upload successful, refreshing data from API');

        // Refresh the data from the API
        try {
          // Prepare the endpoint URL
          let refreshEndpoint = '/competitor-prices/';

          // If using CORS proxy, we need to handle the URL differently
          if (isUsingCorsProxy) {
            const baseUrlParts = API_CONFIG.baseUrl.split('?');
            if (baseUrlParts.length > 1) {
              const proxyUrl = baseUrlParts[0];
              const targetUrl = baseUrlParts[1];
              refreshEndpoint = `${proxyUrl}?${encodeURIComponent(`${targetUrl}/competitor-prices/`)}`;

              console.log('Using direct axios call for refresh with URL:', refreshEndpoint);
              const directResponse = await axios.get(refreshEndpoint);
              const products = directResponse.data.results || directResponse.data;

              // Update our mock products cache with the latest data
              API_CONFIG.mockProducts = products;
              saveMockProductsToStorage(products);

              console.log('Local cache updated with', products.length, 'products');
              return response.data;
            }
          }

          // If not using CORS proxy or if we couldn't parse the URL correctly
          console.log('Using API client for refresh with endpoint:', refreshEndpoint);
          const client = getApiClient();
          const refreshedData = await client.get(refreshEndpoint);
          const products = refreshedData.data.results || refreshedData.data;

          // Update our mock products cache with the latest data
          API_CONFIG.mockProducts = products;
          saveMockProductsToStorage(products);

          console.log('Local cache updated with', products.length, 'products');
        } catch (refreshError) {
          console.error('Failed to refresh data after upload:', refreshError);
        }
      }

      return response.data;
    } catch (error: any) {
      console.error('Error uploading CSV file:', error);

      // Log detailed error information
      if (error.response) {
        console.error('Error response status:', error.response.status);
        console.error('Error response data:', error.response.data);
      } else if (error.request) {
        console.error('No response received from server');
      } else {
        console.error('Error message:', error.message);
      }

      if (API_CONFIG.useMockData) {
        // Return a mock success response even in error case
        return {
          success: true,
          message: `File ${file.name} uploaded successfully (MOCK DATA - NOT SAVED TO DATABASE)`,
          count: Math.floor(Math.random() * 10) + 5
        };
      }

      // If we're not using mock data, rethrow the error to be handled by the caller
      throw error;
    }
  }
};

export { api };