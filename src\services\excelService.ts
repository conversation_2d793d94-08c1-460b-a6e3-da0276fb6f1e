// Define the PriceData interface if it doesn't exist already
interface PriceData {
  id?: string;
  category: string;
  sku_size: string;
  sku_name: string;
  kd_case_price: number;
  kd_unit_price: number;
  kd_price_gram: number;
  selling_price_case: number;
  open_market_price: number;
  ng_price: number;
  small_supermarket_price: number;
  uploadDate: Date;
  fileName: string;
}

import { SKU_CATEGORIES, SKU_SIZE_CATEGORIES } from '../utils/brandUtils';

export class ExcelService {
  // In-memory storage for uploaded files
  private uploadedFiles: PriceData[] = [];

  // Define column indices based on the provided Excel column references
  private readonly COLUMNS = {
    SKU_CATEGORY: 0,      // A
    SKU_SIZE: 1,          // B
    SKU_NAME: 2,          // C
    KD_CASE_PRICE: 5,     // F
    KD_UNIT_PRICE: 6,     // G
    KD_PRICE_GRAM: 7,     // H
    SELLING_PRICE_CASE: 14, // O
    OPEN_MARKET_PRICE: 21,  // V
    NG_PRICE: 28,         // AC
    SMALL_SUPER: 35       // AJ
  };

  public async readDatabaseFiles(): Promise<PriceData[]> {
    try {
      console.log('Fetching CSV file...');

      // Try multiple possible locations for the CSV file
      const possiblePaths = [
        '/database/price-pickup.csv',
        './database/price-pickup.csv',
        '/public/database/price-pickup.csv',
        './public/database/price-pickup.csv',
        '/price-pickup.csv',
        './price-pickup.csv',
        '/src/database/price-pickup.csv',
        './src/database/price-pickup.csv'
      ];

      let csvText = '';
      let fetchSuccess = false;
      let fetchErrors = [];

      for (const path of possiblePaths) {
        try {
          console.log(`Trying to fetch CSV from: ${path}`);
          const response = await fetch(path, {
            // Add cache-busting query parameter
            headers: { 'Cache-Control': 'no-cache' },
            cache: 'no-store'
          });

          if (response.ok) {
            csvText = await response.text();
            console.log(`Successfully fetched CSV from: ${path} (${csvText.length} bytes)`);
            fetchSuccess = true;
            break;
          } else {
            fetchErrors.push(`Failed to fetch from ${path}: ${response.status} ${response.statusText}`);
          }
        } catch (fetchErr) {
          fetchErrors.push(`Error fetching from ${path}: ${fetchErr.message}`);
          // Continue to next path
        }
      }

      if (!fetchSuccess) {
        console.warn('Failed to fetch CSV file from any location. Errors:', fetchErrors);
        return this.createFallbackData('no-csv', 'fetch-failed');
      }

      if (!csvText || csvText.trim().length < 10) {
        console.warn('CSV file was empty or too small:', csvText);
        return this.createFallbackData('empty-csv', 'empty-content');
      }

      const data = await this.processCSVData(csvText, 'price-pickup.csv');
      return data;
    } catch (error) {
      console.error('Error reading database files:', error);
      return this.createFallbackData('general-error', 'read-failed');
    }
  }

  private async processCSVData(csvString: string, fileName: string): Promise<PriceData[]> {
    try {
      console.log('Processing CSV data, length:', csvString.length);

      // Split by either \n or \r\n to handle different line endings
      const lines = csvString.split(/\r?\n/).filter(line => line.trim());
      console.log('Number of lines:', lines.length);

      if (lines.length < 5) {
        console.warn(`CSV file has insufficient rows: ${lines.length} (needed at least 5)`);
        return this.createFallbackData(fileName, 'insufficient-rows');
      }

      // Parse CSV line into array of values
      const parseCSVLine = (line: string): string[] => {
        const values: string[] = [];
        let currentValue = '';
        let insideQuotes = false;

        for (let i = 0; i < line.length; i++) {
          const char = line[i];
          if (char === '"') {
            insideQuotes = !insideQuotes;
          } else if (char === ',' && !insideQuotes) {
            values.push(currentValue.trim());
            currentValue = '';
          } else {
            currentValue += char;
          }
        }
        values.push(currentValue.trim());
        return values;
      };

      const data: PriceData[] = [];

      // Start from row 5 (index 4) as per the Excel reference
      const startRow = 4; // 0-based index for row 5

      // Process data rows starting from row 5
      for (let i = startRow; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue;

        const values = parseCSVLine(line);

        // Safely get a value from the values array with a fallback
        const safeGetValue = (index: number, fallback: string = ''): string => {
          return (index < values.length) ? (values[index]?.trim() || fallback) : fallback;
        };

        // Clean number values
        const cleanNumber = (value: string): number => {
          if (!value || value.trim() === '' || value.trim().toLowerCase() === '#name?') return 0;
          const cleaned = value.replace(/[^0-9.-]/g, '');
          return cleaned ? parseFloat(cleaned) : 0;
        };

        try {
          // Get SKU name from column C
          let skuName = safeGetValue(this.COLUMNS.SKU_NAME);

          // If SKU name is empty, skip this row
          if (!skuName) {
            continue;
          }

          // Get category and validate against known categories
          let category = safeGetValue(this.COLUMNS.SKU_CATEGORY);
          if (!Object.values(SKU_CATEGORIES).includes(category.toUpperCase())) {
            // Try to determine category from SKU name
            if (skuName.toUpperCase().includes('ROLL') || skuName.toUpperCase().includes('DEO')) {
              category = 'DEODORANT';
            } else if (skuName.toUpperCase().includes('TOOTH') || skuName.toUpperCase().includes('PASTE')) {
              category = 'ORAL CARE';
            } else if (skuName.toUpperCase().includes('LOTION') || skuName.toUpperCase().includes('JELLY')) {
              category = 'SKIN CARE';
            } else {
              category = 'NUTRITION'; // Default
            }
          }

          // Get size category and validate against known size categories
          let sizeCategory = safeGetValue(this.COLUMNS.SKU_SIZE);
          if (!Object.values(SKU_SIZE_CATEGORIES).includes(sizeCategory.toUpperCase())) {
            // Default to REGULAR PACK
            sizeCategory = 'REGULAR PACK';
          }

          const priceData: PriceData = {
            category: category,
            sku_size: sizeCategory,
            sku_name: skuName,
            kd_case_price: cleanNumber(safeGetValue(this.COLUMNS.KD_CASE_PRICE, '0')),
            kd_unit_price: cleanNumber(safeGetValue(this.COLUMNS.KD_UNIT_PRICE, '0')),
            kd_price_gram: cleanNumber(safeGetValue(this.COLUMNS.KD_PRICE_GRAM, '0')),
            selling_price_case: cleanNumber(safeGetValue(this.COLUMNS.SELLING_PRICE_CASE, '0')),
            open_market_price: cleanNumber(safeGetValue(this.COLUMNS.OPEN_MARKET_PRICE, '0')),
            ng_price: cleanNumber(safeGetValue(this.COLUMNS.NG_PRICE, '0')),
            small_supermarket_price: cleanNumber(safeGetValue(this.COLUMNS.SMALL_SUPER, '0')),
            uploadDate: new Date(),
            fileName
          };

          // Add the product if it has a name
          if (priceData.sku_name) {
            data.push(priceData);
          }
        } catch (err) {
          console.error(`Error processing row ${i+1}:`, err);
        }
      }

      console.log(`Successfully processed ${data.length} valid rows`);

      // If no data was found, create some dummy data to prevent errors
      if (data.length === 0) {
        console.warn('No valid data rows found in CSV, creating fallback data');
        return this.createFallbackData(fileName, 'no-valid-rows');
      }

      return data;
    } catch (error) {
      console.error('Error processing CSV data:', error);
      return this.createFallbackData(fileName, 'processing-error');
    }
  }

  // Helper method to create fallback data
  private createFallbackData(reason: string, source: string): PriceData[] {
    console.log(`Creating fallback data. Reason: ${reason}, Source: ${source}`);

    return [
      {
        id: `fallback-1-${Date.now()}`,
        category: 'DEODORANT',
        sku_size: 'REGULAR PACK',
        sku_name: 'Example Product 1',
        brand: 'REXONA',  // Add this if needed
        kd_case_price: 1000,
        kd_unit_price: 200,
        kd_price_gram: 10,
        selling_price_case: 1200,
        open_market_price: 1500,
        ng_price: 1400,
        small_supermarket_price: 1600,
        uploadDate: new Date(),
        fileName: `fallback-${source}-${reason}.csv`
      },
      {
        id: `fallback-2-${Date.now()}`,
        category: 'SKIN CARE',
        sku_size: 'SMALL PACK',
        sku_name: 'Example Product 2',
        brand: 'VASELINE',
        kd_case_price: 800,
        kd_unit_price: 150,
        kd_price_gram: 8,
        selling_price_case: 950,
        open_market_price: 1100,
        ng_price: 1050,
        small_supermarket_price: 1200,
        uploadDate: new Date(),
        fileName: `fallback-${source}-${reason}.csv`
      }
    ];
  }

  public async uploadFile(file: File): Promise<PriceData[]> {
    try {
      console.log('Processing uploaded file:', file.name);

      // Read the file content
      const fileContent = await this.readFileContent(file);

      // Process the file based on its type
      let data: PriceData[];
      if (file.name.endsWith('.csv')) {
        data = await this.processCSVData(fileContent, file.name);
      } else if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
        data = await this.processExcelData(fileContent, file.name);
      } else {
        throw new Error('Unsupported file format. Please upload a CSV or Excel file.');
      }

      // Add the processed data to our uploaded files
      this.uploadedFiles = [...this.uploadedFiles, ...data];

      console.log(`Successfully processed ${data.length} products from uploaded file`);
      return data;
    } catch (error) {
      console.error('Error uploading file:', error);
      throw error;
    }
  }

  private async readFileContent(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (event) => {
        if (event.target?.result) {
          resolve(event.target.result as string);
        } else {
          reject(new Error('Failed to read file content'));
        }
      };

      reader.onerror = () => {
        reject(new Error('Error reading file'));
      };

      reader.readAsText(file);
    });
  }

  private async processExcelData(content: string, fileName: string): Promise<PriceData[]> {
    // For now, we'll just process it as CSV since we don't have a proper Excel parser
    // In a real implementation, you would use a library like xlsx or exceljs
    return this.processCSVData(content, fileName);
  }

  // Get all uploaded files
  public getUploadedFiles(): PriceData[] {
    console.log('Getting uploaded files:', this.uploadedFiles.length);
    return this.uploadedFiles;
  }

  public async generateTemplate(): Promise<void> {
    try {
      // Create CSV content for the template
      const headers = [
        'SKU Category', 'SKU Size', 'SKU Name', 'Brand',
        '', '', '', '', 'KD Case Price', 'KD Unit Price', 'KD Price/Gram',
        '', '', '', '', '', '', 'Wholesale Price',
        '', '', '', '', '', '', '', 'Open Market Price',
        '', '', '', '', '', '', '', 'NG Price',
        '', '', '', '', '', '', '', 'Small Supermarket Price'
      ];

      // Add category and size options as comments in the first rows
      const categoryRow = [`Categories: ${SKU_CATEGORIES.join(', ')}`];
      const sizeRow = [`Sizes: ${SKU_SIZE_CATEGORIES.join(', ')}`];
      const instructionRow = ['Please fill in the data starting from row 5. The first 4 rows are for headers and instructions.'];

      // Create the CSV content
      let csvContent = [
        categoryRow.join(','),
        sizeRow.join(','),
        instructionRow.join(','),
        '',  // Empty row
        headers.join(',')
      ].join('\n');

      // Add some example rows
      const exampleRows = [
        ['DEODORANT', 'REGULAR PACK', 'Example Deodorant 50ml', 'BRAND NAME', '', '', '', '', '2400', '400', '', '', '', '', '', '', '', '2400', '', '', '', '', '', '', '', '2500', '', '', '', '', '', '', '', '2300', '', '', '', '', '', '', '', '2600'].join(','),
        ['SKIN CARE', 'SMALL PACK', 'Example Lotion 100ml', 'BRAND NAME', '', '', '', '', '1200', '200', '', '', '', '', '', '', '', '1200', '', '', '', '', '', '', '', '1300', '', '', '', '', '', '', '', '1100', '', '', '', '', '', '', '', '1400'].join(',')
      ];

      csvContent += '\n' + exampleRows.join('\n');

      // Create a Blob with the CSV content
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

      // Create a download link and trigger the download
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);

      link.setAttribute('href', url);
      link.setAttribute('download', 'price_pickup_template.csv');
      link.style.visibility = 'hidden';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log('Template generated successfully');
    } catch (error) {
      console.error('Error generating template:', error);
      throw error;
    }
  }
}

export const excelService = new ExcelService();










































