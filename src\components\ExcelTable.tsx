import React from 'react';

interface PriceData {
  sku_category: string;
  sku_size_category: string;
  gram_unit: number;
  unit_pack: number;
  gram_pack: number;
  pack_case: number;
  kd_case_price: number;
  kd_unit_price: number;
  kd_price_gram: number;
  selling_price_case: number;
  open_market_pack_price: number;
  ng_pack_price: number;
  small_supermarket_pack_price: number;
  created_at: string;
  updated_at: string;
}

const ExcelTable: React.FC<{ data: PriceData[] }> = ({ data }) => {
  if (!data || data.length === 0) return <p>No data available.</p>;

  // Group data by date
  const groupedData = data.reduce((acc, item) => {
    const dateKey = new Date(item.created_at).toLocaleDateString();
    if (!acc[dateKey]) {
      acc[dateKey] = [];
    }
    acc[dateKey].push(item);
    return acc;
  }, {} as Record<string, PriceData[]>);

  // Sort dates in descending order
  const sortedDates = Object.keys(groupedData).sort((a, b) => 
    new Date(b).getTime() - new Date(a).getTime()
  );

  const formatValue = (value: any): string => {
    if (value instanceof Date) {
      return value.toLocaleString();
    }
    if (typeof value === 'number') {
      return value.toLocaleString('en-NG', { 
        style: 'currency', 
        currency: 'NGN',
        minimumFractionDigits: 2
      });
    }
    return value?.toString() || '';
  };

  return (
    <div className="overflow-x-auto">
      {sortedDates.map(dateKey => (
        <div key={dateKey} className="mb-8">
          <div className="bg-blue-100 p-3 rounded-t-lg font-semibold text-blue-800">
            Upload Date: {dateKey}
          </div>
          
          <table className="w-full text-left border">
            <thead className="bg-gray-100">
              <tr>
                <th className="border px-4 py-2">SKU Category</th>
                <th className="border px-4 py-2">Size Category</th>
                <th className="border px-4 py-2">Open Market</th>
                <th className="border px-4 py-2">NG Pack</th>
                <th className="border px-4 py-2">Small Supermarket</th>
                <th className="border px-4 py-2">KD Case Price</th>
              </tr>
            </thead>
            <tbody>
              {groupedData[dateKey].map((row, rowIndex) => (
                <tr key={rowIndex} className="hover:bg-gray-50">
                  <td className="border px-4 py-2">{row.sku_category}</td>
                  <td className="border px-4 py-2">{row.sku_size_category}</td>
                  <td className="border px-4 py-2">{formatValue(row.open_market_pack_price)}</td>
                  <td className="border px-4 py-2">{formatValue(row.ng_pack_price)}</td>
                  <td className="border px-4 py-2">{formatValue(row.small_supermarket_pack_price)}</td>
                  <td className="border px-4 py-2">{formatValue(row.kd_case_price)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ))}
    </div>
  );
};

export default ExcelTable;

