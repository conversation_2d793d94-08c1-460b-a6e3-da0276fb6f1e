import React from 'react';

// Define the available SKU categories with icons and colors
const SKU_CATEGORIES = [
  { id: 'NUTRITION', name: 'NUTRITION', icon: '🍽️', color: 'bg-blue-500' },
  { id: 'ORAL CARE', name: 'ORAL CARE', icon: '🦷', color: 'bg-green-500' },
  { id: 'DEOS', name: 'DEOS', icon: '💨', color: 'bg-purple-500' },
  { id: 'SKIN CARE', name: 'PERSONAL CARE', icon: '🧴', color: 'bg-pink-500' },
  { id: 'DEODORANT', name: 'DEODORANT', icon: '🌿', color: 'bg-teal-500' },
  { id: 'CASE', name: 'CASE', icon: '📦', color: 'bg-orange-500' },
  { id: 'SALVORY', name: 'SALVORY', icon: '🧂', color: 'bg-yellow-500' }
];

// Category Selection Component
interface CategorySelectionProps {
  onCategorySelect: (category: string) => void;
  selectedCategory?: string;
  fontSize?: string;
  onBack?: () => void;
}

const CategorySelection: React.FC<CategorySelectionProps> = ({ 
  onCategorySelect, 
  selectedCategory,
  fontSize = 'text-sm',
  onBack
}) => {
  const handleCategoryClick = (categoryId: string) => {
    onCategorySelect(categoryId);
  };

  return (
    <div className="max-w-md mx-auto bg-white min-h-screen">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded-b-lg shadow-lg">
        <div className="flex items-center mb-2">
          {onBack && (
            <button onClick={onBack} className="mr-3 text-white hover:bg-white/20 p-1 rounded">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
          )}
          <div>
            <h1 className={`${fontSize === 'text-sm' ? 'text-lg' : fontSize} font-bold`}>Price Pickup</h1>
            <p className="text-blue-100 text-sm">Select Product Category</p>
          </div>
        </div>
      </div>

      {/* Task List Header */}
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <h2 className={`${fontSize === 'text-sm' ? 'text-base' : fontSize} font-semibold text-gray-800`}>Category List</h2>
          <button className="text-gray-400 hover:text-gray-600">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </button>
        </div>

        {/* Category Buttons */}
        <div className="space-y-3">
          {SKU_CATEGORIES.map((category, index) => (
            <button
              key={category.id}
              onClick={() => handleCategoryClick(category.id)}
              className={`w-full flex items-center justify-between p-4 rounded-xl shadow-sm border transition-all duration-200 hover:shadow-md transform hover:scale-[1.02] ${
                selectedCategory === category.id 
                  ? 'bg-blue-50 border-blue-200 shadow-md ring-2 ring-blue-300' 
                  : 'bg-white border-gray-200 hover:bg-gray-50'
              }`}
            >
              <div className="flex items-center space-x-4">
                {/* Number Badge */}
                <div className={`w-8 h-8 rounded-full ${category.color} flex items-center justify-center text-white font-bold text-sm shadow-sm`}>
                  {index + 1}
                </div>
                
                {/* Category Info */}
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{category.icon}</span>
                  <span className={`${fontSize === 'text-sm' ? 'text-sm' : fontSize} font-medium text-gray-700`}>
                    {category.name}
                  </span>
                </div>
              </div>

              {/* Check Mark */}
              <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all duration-200 ${
                selectedCategory === category.id 
                  ? 'bg-green-500 border-green-500 scale-110' 
                  : 'border-gray-300'
              }`}>
                {selectedCategory === category.id && (
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                )}
              </div>
            </button>
          ))}
        </div>

        {/* Continue Button */}
        {selectedCategory && (
          <div className="mt-6 animate-fadeIn">
            <button
              onClick={() => handleCategoryClick(selectedCategory)}
              className="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-6 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]"
            >
              Continue to Products
              <svg className="w-5 h-5 ml-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        )}
      </div>

      {/* Add some custom CSS for animations */}
      <style jsx>{`
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        .animate-fadeIn {
          animation: fadeIn 0.3s ease-out;
        }
      `}</style>
    </div>
  );
};

export default CategorySelection;
