import React from 'react';
import { Link, useLocation } from 'react-router-dom';

const Navigation: React.FC = () => {
  const location = useLocation();

  return (
    <nav className="mb-6">
      <ul className="flex space-x-4">
        <li>
          <Link
            to="/price-pickup"
            className={`px-4 py-2 rounded-lg ${
              location.pathname === '/price-pickup'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-200 hover:bg-gray-300'
            }`}
          >
            Price Pickup Form
          </Link>
        </li>
        <li>
          <Link
            to="/dashboard"
            className={`px-4 py-2 rounded-lg ${
              location.pathname === '/dashboard'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-200 hover:bg-gray-300'
            }`}
          >
            Dashboard
          </Link>
        </li>
      </ul>
    </nav>
  );
};

export default Navigation;