export function validateCSVFormat(csvString: string): boolean {
  const rows = csvString.split('\n');
  if (rows.length < 6) return false; // Need at least 6 rows to reach headers

  // Get headers from row 6 and clean them
  const headerRow = rows[5].split(',').map(col => col.trim().toLowerCase());

  // Check for required columns with space-delimited names
  const requiredColumns = [
    ['size'],
    ['sku', 'name'],
    ['selling', 'price', 'case'],
    ['open', 'market', 'pack', 'price'],
    ['ng', 'pack', 'price'],
    ['small', 'supermarket', 'pack', 'price']
  ];

  return requiredColumns.every(words => 
    headerRow.some(col => words.every(word => col.includes(word)))
  );
}
