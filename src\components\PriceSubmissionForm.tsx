import React, { useState } from 'react';
import { api } from '../services/api';
import ProductSelectionForm, { ProductFormData, Product } from './ProductSelectionForm';
import { isUnileverBrand, getBrandFromSku } from '../utils/brandUtils';

interface PriceSubmissionFormProps {
  onSubmitSuccess: () => void;
}

const PriceSubmissionForm: React.FC<PriceSubmissionFormProps> = ({ onSubmitSuccess }) => {
  const [formData, setFormData] = useState<ProductFormData>({
    sku_name: '',
    sku_category: '',
    sku_size_category: '',
    brand: '',
    market_type: '',
    location: '',
    selling_price_case: '',
    selling_price_unit: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);

  // Handle form field changes
  const handleFormChange = (name: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle product selection
  const handleProductSelect = (product: Product | null) => {
    if (!product) {
      setSelectedProduct(null);
      setIsEditMode(false);
      return;
    }

    setSelectedProduct(product);
    setIsEditMode(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    // Test API connection first
    try {
      const connectionTest = await api.testConnection();
      if (!connectionTest.success) {
        setError(`API connection error: ${connectionTest.error}. Please check if the API server is running.`);
        setIsSubmitting(false);
        return;
      }
    } catch (err) {
      console.error('Error testing API connection:', err);
      setError('Could not connect to the API server. Please check if it is running.');
      setIsSubmitting(false);
      return;
    }

    // Validate required fields
    const requiredFields = [
      { name: 'sku_name', label: 'SKU Name' },
      { name: 'sku_category', label: 'SKU Category' },
      { name: 'sku_size_category', label: 'SKU Size Category' },
      { name: 'brand', label: 'Brand' },
      { name: 'market_type', label: 'Market Type' },
      { name: 'location', label: 'Location' },
      { name: 'selling_price_case', label: 'Selling Price (Case)' },
      { name: 'selling_price_unit', label: 'Selling Price (Unit)' }
    ];

    const missingFields = requiredFields.filter(field => !formData[field.name]);

    if (missingFields.length > 0) {
      setError(`Please fill in all required fields: ${missingFields.map(f => f.label).join(', ')}`);
      setIsSubmitting(false);
      return;
    }

    try {
      // Convert string prices to numbers
      const numericPriceCase = typeof formData.selling_price_case === 'string'
        ? parseFloat(formData.selling_price_case)
        : formData.selling_price_case || 0;

      const numericPriceUnit = typeof formData.selling_price_unit === 'string'
        ? parseFloat(formData.selling_price_unit)
        : formData.selling_price_unit || 0;

      // Validate numeric values
      if (isNaN(numericPriceCase) || numericPriceCase < 0) {
        setError('Selling Price (Case) must be a valid positive number');
        setIsSubmitting(false);
        return;
      }

      if (isNaN(numericPriceUnit) || numericPriceUnit < 0) {
        setError('Selling Price (Unit) must be a valid positive number');
        setIsSubmitting(false);
        return;
      }

      // Prepare data for submission
      const submissionData = {
        sku_name: formData.sku_name.trim(),
        sku_category: formData.sku_category.trim(),
        sku_size_category: formData.sku_size_category.trim(),
        brand: formData.brand.trim(),
        market_type: formData.market_type.trim(),
        location: formData.location.trim(),
        selling_price_case: numericPriceCase,
        selling_price_unit: numericPriceUnit
      };

      // Add sku_description if it exists
      if (formData.sku_description) {
        submissionData.sku_description = formData.sku_description.trim();
      }

      // Log the submission data for debugging
      console.log('Submission data:', submissionData);

      // Only update if we have a selected product with an ID and it's not from CSV
      if (isEditMode && selectedProduct && selectedProduct.id && !selectedProduct.id.startsWith('csv-')) {
        // Update existing product
        console.log('Updating existing product:', selectedProduct.id);
        await api.updatePrice(selectedProduct.id, submissionData);
      } else {
        // Submit new data to the API
        console.log('Creating new product entry');
        await api.submitPrice(submissionData);
      }

      // Reset form and state after successful submission
      setFormData({
        sku_name: '',
        sku_category: '',
        sku_size_category: '',
        brand: '',
        market_type: '',
        location: '',
        selling_price_case: '',
        selling_price_unit: ''
      });

      setSelectedProduct(null);
      setIsEditMode(false);

      // Force a refresh of the product list after submission
      setTimeout(() => {
        // Notify parent component of successful submission
        onSubmitSuccess();
      }, 500);
    } catch (err: any) {
      console.error('Error submitting form:', err);

      // Get more detailed error information
      let errorMessage = 'Failed to submit price data. Please try again.';

      if (err.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error('Error response data:', err.response.data);
        console.error('Error response status:', err.response.status);

        if (err.response.data && err.response.data.detail) {
          errorMessage = `Error: ${err.response.data.detail}`;
        } else if (err.response.data && typeof err.response.data === 'string') {
          errorMessage = `Error: ${err.response.data}`;
        } else if (err.response.status === 400) {
          errorMessage = 'Invalid data submitted. Please check your inputs.';
        } else if (err.response.status === 401) {
          errorMessage = 'Authentication required. Please log in.';
        } else if (err.response.status === 403) {
          errorMessage = 'You do not have permission to perform this action.';
        } else if (err.response.status === 404) {
          errorMessage = 'The requested resource was not found.';
        } else if (err.response.status === 500) {
          errorMessage = 'Server error. Please try again later.';
        }
      } else if (err.request) {
        // The request was made but no response was received
        console.error('Error request:', err.request);
        errorMessage = 'No response from server. Please check your connection.';
      } else {
        // Something happened in setting up the request that triggered an Error
        console.error('Error message:', err.message);
        errorMessage = `Error: ${err.message}`;
      }

      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-xl font-semibold mb-4">Submit Price Data</h2>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Use the shared ProductSelectionForm component */}
          <ProductSelectionForm
            formData={formData}
            onChange={handleFormChange}
            onProductSelect={handleProductSelect}
            isEditMode={isEditMode}
            className="col-span-2"
            fontSize="text-[10px]"
          />
        </div>

        <div className="mt-6">
          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
          >
            {isSubmitting
              ? 'Submitting...'
              : (isEditMode && selectedProduct && !selectedProduct.id?.startsWith('csv-'))
                ? 'Update Price Data'
                : 'Submit Price Data'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default PriceSubmissionForm;
