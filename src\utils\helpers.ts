/**
 * Debounce function to limit how often a function can be called
 * @param func The function to debounce
 * @param wait The time to wait in milliseconds
 * @returns A debounced version of the function
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;

  return function(...args: Parameters<T>): void {
    const later = () => {
      timeout = null;
      func(...args);
    };

    if (timeout !== null) {
      clearTimeout(timeout);
    }
    timeout = setTimeout(later, wait);
  };
}

/**
 * Normalize a string for case-insensitive comparison
 * @param str The string to normalize
 * @returns Normalized string (lowercase, trimmed)
 */
export function normalizeString(str: string): string {
  return str.toLowerCase().trim();
}

/**
 * Check if a string contains another string (case insensitive)
 * @param haystack The string to search in
 * @param needle The string to search for
 * @returns True if the needle is found in the haystack
 */
export function containsText(haystack: string, needle: string): boolean {
  return normalizeString(haystack).includes(normalizeString(needle));
}
