import React from 'react';

// Market type options with icons
const MARKET_TYPES = [
  { value: 'OPEN_MARKET', label: 'Open Market', icon: '🏪', color: 'bg-orange-500', description: 'Traditional open markets and street vendors' },
  { value: 'NG', label: 'NG', icon: '🏢', color: 'bg-blue-500', description: 'NG retail outlets and stores' },
  { value: 'SMALL_SUPERMARKET', label: 'Small Supermarket', icon: '🛒', color: 'bg-green-500', description: 'Small to medium supermarkets' },
  { value: 'WHOLESALE', label: 'Wholesale', icon: '📦', color: 'bg-purple-500', description: 'Wholesale and bulk distribution' }
];

interface NewSKUMarketSelectionProps {
  selectedCategory: string;
  onMarketSelect: (market: string) => void;
  onBack: () => void;
  fontSize?: string;
}

const NewSKUMarketSelection: React.FC<NewSKUMarketSelectionProps> = ({
  selectedCategory,
  onMarketSelect,
  onBack,
  fontSize = 'text-sm'
}) => {
  return (
    <div className="max-w-md mx-auto bg-white min-h-screen">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-500 to-blue-600 text-white p-4 rounded-b-lg shadow-lg">
        <div className="flex items-center mb-2">
          <button onClick={onBack} className="mr-3 text-white hover:bg-white/20 p-1 rounded">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <div>
            <h1 className={`${fontSize === 'text-sm' ? 'text-lg' : fontSize} font-bold`}>Create New SKU</h1>
            <p className="text-blue-100 text-sm">Step 2: Select Market Type</p>
          </div>
        </div>
      </div>

      {/* Market Selection */}
      <div className="p-4">
        <div className="mb-4">
          <h2 className={`${fontSize === 'text-sm' ? 'text-base' : fontSize} font-semibold text-gray-800 mb-2`}>
            Choose Market Type
          </h2>
          <p className="text-gray-600 text-sm">Category: <span className="font-medium text-green-600">{selectedCategory}</span></p>
        </div>

        {/* Market Type Buttons */}
        <div className="space-y-3">
          {MARKET_TYPES.map((market, index) => (
            <button
              key={market.value}
              onClick={() => onMarketSelect(market.value)}
              className="w-full p-4 rounded-xl shadow-sm border border-gray-200 hover:bg-gray-50 hover:shadow-md transition-all duration-200 transform hover:scale-[1.02] text-left"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  {/* Number Badge */}
                  <div className={`w-8 h-8 rounded-full ${market.color} flex items-center justify-center text-white font-bold text-sm shadow-sm`}>
                    {index + 1}
                  </div>
                  
                  {/* Market Info */}
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{market.icon}</span>
                    <div>
                      <span className={`${fontSize === 'text-sm' ? 'text-sm' : fontSize} font-medium text-gray-700 block`}>
                        {market.label}
                      </span>
                      <span className="text-xs text-gray-500">
                        {market.description}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Arrow */}
                <div className="text-gray-400">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </button>
          ))}
        </div>

        {/* Progress Indicator */}
        <div className="mt-6 bg-gray-50 rounded-lg p-3">
          <div className="flex items-center justify-between mb-2">
            <span className="text-xs font-medium text-gray-600">Progress</span>
            <span className="text-xs text-gray-500">2 of 3 steps</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div className="bg-green-500 h-2 rounded-full" style={{ width: '66%' }}></div>
          </div>
          <div className="flex justify-between mt-2 text-xs text-gray-500">
            <span>✓ Category</span>
            <span className="font-medium text-green-600">Market Type</span>
            <span>Product Details</span>
          </div>
        </div>

        {/* Info Box */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mt-4">
          <div className="flex items-start">
            <svg className="w-5 h-5 text-blue-500 mt-0.5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div>
              <p className="text-blue-800 text-xs font-medium">Market Type Selection</p>
              <p className="text-blue-700 text-xs mt-1">
                Choose the market type where this product will be sold. This affects pricing and distribution.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NewSKUMarketSelection;
