{"name": "price-pickup-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "vercel-build": "node build.cjs", "preview": "vite preview"}, "dependencies": {"@types/react-datepicker": "^7.0.0", "axios": "^1.6.2", "react": "^18.2.0", "react-datepicker": "^8.3.0", "react-dom": "^18.2.0", "react-router-dom": "^6.21.1", "xlsx": "^0.18.5"}, "devDependencies": {"@types/node": "^22.14.1", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.21", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "terser": "^5.29.2", "typescript": "^5.3.3", "vite": "^5.1.0"}}